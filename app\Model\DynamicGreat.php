<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\DbConnection\Model\Model;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

/**
 * @property int $id
 * @property int $user_id
 * @property int $dynamic_id
 * @property int $createtime
 */
class DynamicGreat extends Model
{
    public bool $timestamps = false;

    protected ?string $table = 'dynamic_great';
    protected array $fillable = [];
    protected array $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'dynamic_id' => 'integer',
        'createtime' => 'integer'
    ];

    #[Inject]
    private Redis $redis;

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix', '');
    }

    /**
     * 获取用户别名
     */
    private function getUserAlias(int $user_id, int $target_user_id): string
    {
        $alias_name = $this->redis->hget($this->getRedisPrefix() . "alias_user_{$user_id}", (string)$target_user_id);
        return $alias_name === false ? '' : $alias_name;
    }

    /**
     * 处理用户列表数据
     */
    private function processUserList(array $list, int $user_id, bool $include_time = false): array
    {
        foreach ($list as $index => $user) {
            $list[$index]['alias_name'] = $this->getUserAlias($user_id, $user['user_id']);
            $list[$index]['avatar'] = get_avatar($user['avatar']);

            if ($include_time && isset($user['createtime'])) {
                $list[$index]['createtime_attr'] = last_time($user['createtime']);
            }
        }

        return $list;
    }

    /**
     * 获取动态点赞用户列表
     */
    public function dynamic_great_list($user_id, $dynamic_id, $page, $limit)
    {
        try {
            $list = self::query()
                ->leftJoin('user', 'user.id', '=', 'dynamic_great.user_id')
                ->where('dynamic_id', $dynamic_id)
                ->orderBy('dynamic_great.createtime', 'desc')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->select('user_id', 'avatar', 'nickname', 'girl_switch', 'is_vip')
                ->get();

            if (!$list) {
                $result = [];
            } else {
                $list = $list->toArray();
                $result = $this->processUserList($list, $user_id);
            }
        } catch (\Exception $e) {
            $result = [];
        }

        return $result;
    }
    /**
     * 获取用户收到的点赞列表
     */
    public function dynamic_user_great_list($user_id, $page, $limit)
    {
        try {
            $list = self::query()
                ->leftJoin('user', 'user.id', '=', 'dynamic_great.user_id')
                ->where('to_user_id', $user_id)
                ->orderBy('dynamic_great.createtime', 'desc')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->select('user_id', 'avatar', 'nickname', 'girl_switch', 'is_vip', 'dynamic_great.createtime')
                ->get();

            if (!$list) {
                $result = [];
            } else {
                $list = $list->toArray();
                $result = $this->processUserList($list, $user_id, true);
            }
        } catch (\Exception $e) {
            $result = [];
        }

        return $result;
    }
}
