<?php

declare(strict_types=1);

namespace App\Controller;

use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\DbConnection\Db;
use Hyperf\HttpServer\Annotation\Middlewares;
use App\Middleware\Auth\TokenMiddleware;
use Hyperf\Context\Context;
use Yansongda\HyperfPay\Pay;
use App\Model\User;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Hyperf\Nsq\Nsq;

#[AutoController]
#[Middlewares([TokenMiddleware::class])]
class MoneyController extends AbstractController
{
    const MER_ID = '';
    const VISA_URL = 'https://gateway.sslonlinepay.com/api/payOrder.aspx';
    const VISA_KEY = '';
    const VISA_TYPE = 'USD';

    const PAY_TYPES = [
        'aliapp',
        'aliweb',
        'alimp',
        'aliscan',
        'wxapp',
        'wxmp',
        'wxweb',
        'yue',
        'pg',
        'wxwap',
        'visa',
        'lxpay',
        'paypal',
        'welopay',
        'welopay_paypal'
    ];

    const ORDER_TYPE_VIP = 'VI';
    const ORDER_TYPE_RECHARGE = 'RE';

    #[Inject]
    private Nsq $nsq;
    #[Inject]
    private Redis $redis;
    #[Inject]
    private Pay $payclint;

    /**
     * 获取当前用户ID
     */
    private function getCurrentUserId(): int
    {
        $user_id = Context::get('user_id', 0);
        if (!$user_id) {
            throw new \Exception(\Hyperf\Translation\trans('params.loginError'), 10001);
        }
        return (int) $user_id;
    }

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix', '');
    }



    /**
     * 统一异常处理
     */
    private function handleException(\Exception $e): array
    {
        return error($e->getMessage(), null, $e->getCode() ?: '10001');
    }

    /**
     * 验证订单信息
     */
    private function validateOrder(string $order_sn): array
    {
        $type = substr($order_sn, 0, 2);

        switch ($type) {
            case self::ORDER_TYPE_VIP:
                $info = Db::table('vip_order')->where('order_sn', $order_sn)->first();
                $money_field = 'price';
                $text = \Hyperf\Translation\trans('params.buyVip');
                break;
            case self::ORDER_TYPE_RECHARGE:
                $info = Db::table('recharge_order')->where('order_sn', $order_sn)->first();
                $money_field = 'amount';
                $text = \Hyperf\Translation\trans('params.rechargeDiamond');
                break;
            default:
                throw new \Exception(\Hyperf\Translation\trans('params.orderError'));
        }

        if (!$info) {
            throw new \Exception(\Hyperf\Translation\trans('params.orderError'));
        }

        if ($info['status'] == '2') {
            throw new \Exception(\Hyperf\Translation\trans('params.expired'));
        }

        if ($info['status'] == '1') {
            throw new \Exception(\Hyperf\Translation\trans('params.paid'));
        }

        return [
            'info' => $info,
            'money' => $info[$money_field],
            'text' => $text,
            'type' => $type
        ];
    }

    /**
     * 验证输入参数
     */
    private function validatePaymentInputs(string $real_email, string $real_phone, string $pay_type): void
    {
        if (!filter_var($real_email, FILTER_VALIDATE_EMAIL) && $real_email) {
            throw new \Exception(\Hyperf\Translation\trans('params.emailError'));
        }

        if (!preg_match('/^1\d{10}$/', $real_phone) && $real_phone) {
            throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
        }

        if (!in_array($pay_type, self::PAY_TYPES)) {
            throw new \Exception(\Hyperf\Translation\trans('params.choosePayType'));
        }
    }

    /**
     * 支付处理主方法
     */
    public function pay(RequestInterface $req)
    {
        try {
            $order_sn = $req->input('order_sn', '');
            $pay_type = $req->input('pay_type', '');
            $pay_proof = $req->input('pay_proof', '');
            $real_name = $req->input('real_name', '');
            $real_phone = $req->input('real_phone', '');
            $real_email = $req->input('real_email', '');

            $this->validatePaymentInputs($real_email, $real_phone, $pay_type);

            $orderData = $this->validateOrder($order_sn);
            $info = $orderData['info'];
            $money = $orderData['money'];
            $text = $orderData['text'];
            $type = $orderData['type'];

            if ($pay_type === 'yue' && $type === self::ORDER_TYPE_RECHARGE) {
                $text = \Hyperf\Translation\trans('params.exchanged');
            }

            $result = $this->processPayment($order_sn, $pay_type, $money, $text, $info, $type, $pay_proof, $real_name, $real_phone, $real_email, $req);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }

        return $result;
    }

    /**
     * 处理不同支付方式
     */
    private function processPayment(string $order_sn, string $pay_type, float $money, string $text, array $info, string $type, string $pay_proof, string $real_name, string $real_phone, string $real_email, RequestInterface $req): array
    {
        $prefix = $this->getRedisPrefix();
        switch ($pay_type) {
            case 'aliapp':
            case 'aliweb':
            case 'alimp':
            case 'aliscan':
            case 'wxapp':
            case 'wxweb':
            case 'wxwap':
                return $this->handleThirdPartyPay($order_sn, $pay_type, $money, $text, $prefix);

            case 'wxmini':
                return $this->handleWxMiniPay($order_sn, $pay_type, $money, $text);

            case 'yue':
                return $this->handleBalancePay($order_sn, $money, $text, $info, $type);

            case 'pg':
                return $this->handleApplePay($order_sn, $pay_proof, $info, $type, $money);

            case 'visa':
                return $this->handleVisaPay($order_sn, $money, $real_name, $real_phone, $real_email, $type);

            case 'lxpay':
                return $this->handleLxPay($order_sn, $money, $text);

            case 'welopay':
            case 'welopay_paypal':
                return $this->handleWeloPay($order_sn, $money, $info, $pay_type, $req);

            case 'paypal':
                return $this->handlePaypalPay($order_sn, $money, $type);

            default:
                throw new \Exception(\Hyperf\Translation\trans('params.choosePayType'));
        }
    }

    /**
     * 处理第三方支付（支付宝、微信等）
     */
    private function handleThirdPartyPay(string $order_sn, string $pay_type, float $money, string $text, string $prefix): array
    {
        $str = $this->submit_other_api($order_sn, $pay_type, $money, $text, '');

        if (in_array($pay_type, ['alimp', 'aliweb'])) {
            $this->redis->setex($prefix . 'order_sn_' . $pay_type . '_' . $order_sn, 300, $str);
            $result = [
                'price' => $money,
                'pay' => $str,
                'wechat_pay' => (object)[],
                'url' => "https://hwsj-api.jiangkukeji.cn/index/pay_page?order_sn={$order_sn}&pay_type={$pay_type}"
            ];
        } else {
            $result = [
                'price' => $money,
                'pay' => $str,
                'wechat_pay' => (object)[],
                'url' => ''
            ];
        }

        return success(\Hyperf\Translation\trans('params.orderSuccess'), $result);
    }

    /**
     * 处理微信小程序支付
     */
    private function handleWxMiniPay(string $order_sn, string $pay_type, float $money, string $text): array
    {
        $str = $this->submit_other_api($order_sn, $pay_type, $money, $text, '');
        $result = [
            'price' => $money,
            'pay' => $str,
            'wechat_pay' => (object)[],
            'url' => ''
        ];

        return success(\Hyperf\Translation\trans('params.orderSuccess'), $result);
    }

    /**
     * 处理余额支付
     */
    private function handleBalancePay(string $order_sn, float $money, string $text, array $info, string $type): array
    {
        $my_money = Db::table('user')->where('id', $info['user_id'])->value('money');
        if ($my_money < $money) {
            throw new \Exception(\Hyperf\Translation\trans('params.balanceError'));
        }

        User::money('-' . $money, $info['user_id'], $text, '1');

        if ($type === self::ORDER_TYPE_VIP) {
            $this->vip_buy_order($order_sn, 'yue', time(), $money);
        } else {
            $this->recharge_order($order_sn, '4', time(), $money);
        }

        return success(\Hyperf\Translation\trans('params.success'), (object)[]);
    }
    /**
     * 处理苹果内购支付
     */
    private function handleApplePay(string $order_sn, string $pay_proof, array $info, string $type, float $money): array
    {
        if (empty($pay_proof)) {
            throw new \Exception(\Hyperf\Translation\trans('params.ticketError'));
        }

        $res = $this->validate_applepay_panduan($pay_proof, true);
        if (intval($res['status']) !== 0) {
            throw new \Exception(\Hyperf\Translation\trans('params.error'));
        }

        $ios_bundle_id = $this->redis->hmget($this->getRedisPrefix() . 'global_config', ['ios_bundle_id']);
        if ($ios_bundle_id['ios_bundle_id'] !== $res['receipt']['bundle_id']) {
            throw new \Exception(\Hyperf\Translation\trans('params.error'));
        }

        $good_code = $res['receipt']['in_app'][0]['product_id'];
        if ($good_code !== $info['good_code']) {
            throw new \Exception(\Hyperf\Translation\trans('params.productError'));
        }

        $transaction_id = $res['receipt']['in_app'][0]['transaction_id'];
        $table = $type === self::ORDER_TYPE_VIP ? 'vip_order' : 'recharge_order';
        $exists = Db::table($table)->where('transaction_id', $transaction_id)->exists();

        if ($exists) {
            throw new \Exception(\Hyperf\Translation\trans('params.ticketUsed'));
        }

        if ($type === self::ORDER_TYPE_VIP) {
            $this->vip_buy_order($order_sn, 'pg', $transaction_id, $money);
        } else {
            $this->recharge_order($order_sn, '3', $transaction_id, $money);
        }

        return success(\Hyperf\Translation\trans('params.success'), (object)[]);
    }

    /**
     * 处理Visa支付
     */
    private function handleVisaPay(string $order_sn, float $money, string $real_name, string $real_phone, string $real_email, string $type): array
    {
        $res = $this->visa_pay($order_sn, $money, $real_name, $real_phone, $real_email, $type);

        if ($res === '支付失败') {
            throw new \Exception(\Hyperf\Translation\trans('params.error'));
        }

        return success(\Hyperf\Translation\trans('params.success'), ['res' => $res]);
    }

    /**
     * 处理蓝新金流支付
     */
    private function handleLxPay(string $order_sn, float $money, string $text): array
    {
        $url = env('uri') . 'api/index/lx_pay?';
        $notify_url = env('uri') . 'api/index/notify';
        $post_data = http_build_query([
            'order_sn' => $order_sn,
            'money' => $money,
            'notify_url' => $notify_url,
            'desc' => $text
        ]);

        $result = file_get_contents($url . $post_data);
        $res = json_decode($result, true);

        if ($res['code'] !== '1') {
            throw new \Exception(\Hyperf\Translation\trans('params.error'));
        }

        return success(\Hyperf\Translation\trans('params.success'), ['res' => $res]);
    }
    /**
     * 处理WeloPay支付
     */
    private function handleWeloPay(string $order_sn, float $money, array $info, string $pay_type, RequestInterface $req): array
    {
        $email = Db::table('user')->where('id', $info['user_id'])->value('email');
        if (!$email) {
            throw new \Exception('Please bind your email');
        }

        $url_base = $pay_type === 'welopay_paypal' ? 'welopay/welopay_paypal' : 'welopay/welopay';
        $notify_base = $pay_type === 'welopay_paypal' ? 'welopay/welopay_paypal_notify' : 'welopay/welopay_notify';

        $url = env('url') . $url_base . '?';
        $notify_url = env('url') . $notify_base;
        $successUrl = env('url') . 'welopay/error';
        $errorUrl = env('url') . 'welopay/error';

        $post_data = http_build_query([
            'order_no' => $order_sn,
            'amount' => $money,
            'notify_url' => $notify_url,
            'shopper_id' => $info['user_id'],
            'shopper_email' => $email,
            'cancel_url' => $errorUrl,
            'return_url' => $successUrl,
            'shopper_phone' => $req->input('shopper_phone'),
            'billing_state' => $req->input('billing_state'),
            'billing_city' => $req->input('billing_city'),
            'billing_address' => $req->input('billing_address'),
            'billing_postal_code' => $req->input('billing_postal_code'),
            'billing_country' => $req->input('billing_country')
        ]);

        $result = file_get_contents($url . $post_data);
        $res = json_decode($result, true);

        if ($res['code'] !== '1') {
            throw new \Exception(\Hyperf\Translation\trans('params.error'));
        }

        return success(\Hyperf\Translation\trans('params.success'), ['res' => $res['data']]);
    }

    /**
     * 处理PayPal支付
     */
    private function handlePaypalPay(string $order_sn, float $money, string $type): array
    {
        $res = $this->paypal_pay($money, [], env('url') . 'money/notify_paypal', env('url') . 'gw/payments.html');

        if (!$res || !isset($res['id'])) {
            throw new \Exception(\Hyperf\Translation\trans('params.error'));
        }

        $table = $type === self::ORDER_TYPE_VIP ? 'vip_order' : 'recharge_order';
        Db::table($table)->where('order_sn', $order_sn)->update(['paypal_token' => $res['id']]);

        $links = array_column($res['links'], 'href', 'rel');
        $url = $links['payer-action'];

        return success(\Hyperf\Translation\trans('params.success'), ['url' => $url]);
    }


    /**
     * 蓝新金流支付回调处理
     * @param RequestInterface $request
     * @return bool
     */
    public function notify_lx_pay(RequestInterface $request)
    {
        $order_sn = $request->input('out_order_no');
        $amount   = $request->input('amount');
        $trans    = $request->input('order_no');
        if (!$order_sn || !$amount) return false;
        $this->notify_true($order_sn, $trans, $amount, 'lxpay');
        return true;
    }


    /**
     * welopay支付
     * @param RequestInterface $request
     * @return bool
     */
    public function notify_welo_pay(RequestInterface $request)
    {
        $order_sn = $request->input('out_order_no');
        $amount   = $request->input('amount');
        $trans    = $request->input('order_no');
        $pay_type = $request->input('pay_type') ?? 'welopay';
        if (!$order_sn || !$amount) return false;
        $this->notify_true($order_sn, $trans, $amount, $pay_type);
        return true;
    }

    /**
     * @remark 获取token
     */
    public function getAccessToken()
    {
        $clientId = env('paypal_client_id', '');
        $secret = env('paypal_secret', '');
        $response = $this->sendRequest('https://api-m.sandbox.paypal.com/v1/oauth2/token', [
            'grant_type' => 'client_credentials'
        ], [
            'Accept: application/json',
            'Accept-Language: en_US',
            'Authorization: Basic ' . base64_encode($clientId . ':' . $secret),
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        if ($response && isset($response['access_token'], $response['expires_in'])) {
            $token = $response['access_token'];
            return $token;
        }
        return null;
    }


    /**
     * @remark 创建订单
     * @param $amount
     * @param array $items
     * @param string $returnUrl
     * @param string $cancelUrl
     * @return false|mixed|null
     */
    public function paypal_pay($amount, $items = [], $returnUrl = 'https://example.com/returnUrl', $cancelUrl = 'https://example.com/cancelUrl')
    {
        if (!$token = $this->getAccessToken()) {
            return false;
        }

        foreach ($items as &$item) {
            $item['category'] = 'DIGITAL_GOODS';
        }

        $data = [
            "intent"         => "CAPTURE",
            "payment_source" => [
                "paypal" => [
                    "experience_context" => [
                        "payment_method_preference" => "IMMEDIATE_PAYMENT_REQUIRED",
                        "landing_page"              => "LOGIN",
                        "shipping_preference"       => "NO_SHIPPING",
                        "user_action"               => "PAY_NOW",
                        "return_url"                => $returnUrl,
                        "cancel_url"                => $cancelUrl
                    ]
                ]
            ],
            "purchase_units" => [
                [
                    "invoice_id" => uniqid(),
                    "amount"     => [
                        "currency_code" => "USD",
                        "value"         => $amount,
                        "breakdown"     => [
                            "item_total" => [
                                "currency_code" => "USD",
                                "value"         => $this->calculateItemTotal($items)
                            ],
                            "shipping"   => [
                                "currency_code" => "USD",
                                "value"         => "0.00"
                            ]
                        ]
                    ],
                    "items"      => $items
                ]
            ]
        ];

        return $this->sendRequest('https://api-m.paypal.com/v2/checkout/orders/', $data, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $token
        ]);
    }

    private function sendRequest($url, $data = [], $headers = [])
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);

        if ($data) {
            if (in_array('Content-Type: application/x-www-form-urlencoded', $headers)) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            } else {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        if ($headers) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }

        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
            return null;
        }
        curl_close($ch);
        return json_decode($result, true);
    }

    private function calculateItemTotal($items)
    {
        $total = 0;
        foreach ($items as $item) {
            $total += bcmul($item['unit_amount']['value'], $item['quantity'], 2);
        }
        return $total;
    }

    public function notify_paypal(RequestInterface $request, \Hyperf\HttpServer\Response $response)
    {
        $paypal_token = $request->input('token');
        $payerId      = $request->input('PayerID');
        $successUrl   = env('url') . 'gw/payment.html';
        $errorUrl     = env('url') . 'gw/payments.html';
        $info         = Db::table('vip_order')->where('paypal_token', $paypal_token)->first();
        if (!$info) {
            $info = Db::table('recharge_order')->where('paypal_token', $paypal_token)->first();
        }
        if (!$info) {
            return $response->redirect($errorUrl);
        }
        $order_sn = $info['order_sn'];
        $type = substr($order_sn, 0, 2);
        switch ($type) {
            case 'VI':
                $this->notify_true($order_sn, $payerId, $info['price'], 'paypal');
                break;
            case 'RE':
                $this->notify_true($order_sn, $payerId, $info['amount'], '5');
                break;
        }
        return $response->redirect($successUrl);
    }





    /**
     * Visa支付处理
     */
    private function visa_pay(string $order_sn, float $money, string $real_name, string $real_phone, string $real_email, string $type = 'VI'): string
    {
        $product_name = $type === 'VI' ? 'upgrade' : 'recharge';
        $product_sn = bin2hex(random_bytes(16));

        $param = [
            'merchant_id' => (self::MER_ID * 818) + 5201314,
            'order_type' => 0,
            'gw_version' => 'php(J8.2)',
            'merch_order_ori_id' => $order_sn,
            'merch_order_id' => $order_sn,
            'merch_order_date' => date('YmdHis'),
            'price_currency' => self::VISA_TYPE,
            'price_amount' => $money,
            'url_sync' => env('url') . 'money/notifyx_visa',
            'url_succ_back' => env('url') . 'gw/payment.html',
            'url_fail_back' => env('url') . 'gw/payments.html',
            'bill_email' => $real_email,
            'bill_phone' => $real_phone,
            'delivery_name' => $real_name,
            'delivery_email' => $real_email,
            'delivery_phone' => $real_phone,
            'product_name' => $product_name,
            'product_sn' => $product_sn,
            'quantity' => '1',
            'unit' => '个',
            'guid' => env('url') . $product_sn,
            'signature' => md5(self::VISA_KEY . self::MER_ID . $order_sn . self::VISA_TYPE . $money)
        ];

        $res = $this->curlPost(self::VISA_URL, $param, []);
        $xml = simplexml_load_string($res);
        $xmlArray = json_decode(json_encode($xml), true);

        return isset($xmlArray['payment_url']) && $xmlArray['payment_url']
            ? base64_decode($xmlArray['payment_url'])
            : '支付失败';
    }

    /**
     * cURL POST请求
     */
    private function curlPost(string $url, array $data, array $headers = []): string
    {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($data),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
        ]);

        if (!empty($headers)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        }

        $response = curl_exec($curl);
        $errno = curl_errno($curl);
        curl_close($curl);

        if ($errno) {
            return '';
        }

        return $response ?: '';
    }

    /**
     * visa回调
     * @param RequestInterface $request
     * @return string
     */
    public function notifyx_visa(RequestInterface $request)
    {
        $param = $request->post();
        if ($param['status'] == 'Y') {
            $order_sn = $param['merch_order_id'];
            $money    = $param['price_amount'];
            $this->notify_true($order_sn, '', $money, 'visa');
        }
        return 'ISRESPONSION';
    }


    public function submit_other_api($order_sn, $meth, $money, $text, $openid)
    {
        $pay = $this->payclint;
        switch ($meth) {
            case 'wxmini':
                $order = [
                    'out_trade_no' => $order_sn,
                    'description'  => $text,
                    'amount'       => [
                        'total'    => intval($money * 100),
                        'currency' => 'CNY',
                    ],
                    'payer'        => ['openid' => $openid,]
                ];
                $res   = $pay->wechat()->mini($order);
                break;
            case 'wxmp':
                $order = [
                    'out_trade_no' => $order_sn,
                    'description'  => $text,
                    'amount'       => [
                        'total' => intval($money * 100),
                    ],
                    'payer'        => ['openid' => $openid,],
                ];
                $res   = $pay->wechat()->mp($order);
                break;
            case 'wxapp':
                $order = [
                    'out_trade_no' => $order_sn,
                    'description'  => $text,
                    'amount'       => [
                        'total' => intval($money * 100),
                    ],
                ];
                $res = $pay->wechat()->app($order);
                var_dump($res);
                break;

            case 'wxweb':
                $order = [
                    'out_trade_no' => $order_sn,
                    'description'  => $text,
                    'amount'       => [
                        'total' => intval($money * 100),
                    ],
                ];
                $res   = $pay->wechat()->scan($order);
                var_dump($res);
                break;
            case 'aliapp':
                $res = $pay->alipay()->app([
                    'out_trade_no' => $order_sn,
                    'total_amount' => $money,
                    'subject'      => $text,
                ]);
                $res = $res->getBody()->getContents();
                break;
            case 'aliweb':
                $res = $pay->alipay()->web([
                    'out_trade_no' => $order_sn,
                    'total_amount' => $money,
                    'subject'      => $text,
                ]);
                $res = $res->getBody()->getContents();
                break;
            case 'alimp':
                $res = $pay->alipay()->wap([
                    'out_trade_no' => $order_sn,
                    'total_amount' => $money,
                    'subject'      => $text,
                    '_method'      => 'get',
                ]);
                $res = $res->getBody()->getContents();
                break;
            case 'aliscan':
                $res = $pay->alipay()->scan([
                    'out_trade_no' => time(),
                    'total_amount' => 0.01,
                    'subject'      => 'yansongda 测试 - 01',
                ]);
                break;
        }
        return $res;
    }


    public function notify_wxpay(RequestInterface $req)
    {
        $data     = $req->all();
        $result   = $this->payclint->wechat()->callback($data);
        $resource = $result->resource;
        var_dump($resource);
        if ($resource['original_type'] == 'transaction' && $resource['ciphertext']['trade_state'] == 'SUCCESS') {
            $order_sn = ($result->resource)['ciphertext']['out_trade_no'];
            $money    = $resource['ciphertext']['amount']['payer_total'] * 0.01;
            $out_sn   = $resource['ciphertext']['transaction_id'];
            $this->notify_true($order_sn, $out_sn, $money, '2');
        }
        return $this->payclint->wechat()->success();
    }

    /**
     * 支付宝支付回调
     */
    public function notify_alipay(RequestInterface $req)
    {
        $data = $req->all();
        $order = ['out_trade_no' => $data['out_trade_no']];
        $result = $this->payclint->alipay()->find($order);
        $resource = $result->all();

        if (isset($resource['msg']) && $resource['msg'] === 'Success' && $resource['trade_status'] === 'TRADE_SUCCESS') {
            $order_sn = $resource['out_trade_no'];
            $money = $data['buyer_pay_amount'];
            $out_sn = $resource['trade_no'];
            $this->notify_true($order_sn, $out_sn, $money, '1');
            return $this->payclint->alipay()->success();
        }
    }


    /**
     * 支付成功通知处理
     * 支付类型:1=支付宝,2=微信,3=苹果内购,4=余额
     */
    private function notify_true(string $order_sn, string $out_sn, float $money, string $pay_type): void
    {
        $type = substr($order_sn, 0, 2);

        if ($type === self::ORDER_TYPE_VIP) {
            $this->vip_buy_order($order_sn, $pay_type, $out_sn, $money);
        } elseif ($type === self::ORDER_TYPE_RECHARGE) {
            $this->recharge_order($order_sn, $pay_type, $out_sn, $money);
        }
    }


    /**
     * IOS内购验证票据
     * @param string $receipt_data 付款后凭证
     * @return array                验证是否成功
     */
    private function validate_applepay($receipt_data, $sandbox = false)
    {
        $jsonData = array('receipt-data' => $receipt_data);
        $post_json = json_encode($jsonData);
        if ($sandbox) {
            $url = "https://buy.itunes.apple.com/verifyReceipt"; //正式环境
        } else {
            $url = "https://sandbox.itunes.apple.com/verifyReceipt"; //沙盒环境
        }
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_json);
        $result = curl_exec($ch);
        curl_close($ch);
        return json_decode($result, true);
    }

    private function validate_applepay_panduan($receipt_data, $sandbox)
    {
        $res = $this->validate_applepay($receipt_data, $sandbox);
        if ($res['status'] == '21007') {
            $res = $this->validate_applepay($receipt_data, false);
        } elseif ($res['status'] == '21008') {
            $res = $this->validate_applepay($receipt_data, true);
        }
        return $res;
    }



    public function recharge_order($order_sn, $pay_type, $out_sn, $money)
    {
        $info = Db::table('recharge_order')->where('order_sn', $order_sn)->first();
        if ($info['status'] != '0') {
            return;
        }

        $time = time();
        $save               = [];
        $save['paytime']    = $time;
        $save['updatetime'] = $time;
        $save['status']     = '1';
        $save['out_sn']     = $out_sn;
        $save['payamount']  = $money;
        $msg                = '';
        switch ($pay_type) {
            case '1':
                $save['paytype'] = '1';
                $msg             = \Hyperf\Translation\trans('params.rechargeDiamond');
                break;
            case '2':
                $save['paytype'] = '2';
                $msg             = \Hyperf\Translation\trans('params.rechargeDiamond');
                break;
            case '4':
                $save['paytype'] = '4';
                $msg             = \Hyperf\Translation\trans('params.exchanged');
                User::money($info['payamount'], $info['user_id'], $msg);
                break;
            case '3':
                $save['paytype'] = '3';
                $msg             = \Hyperf\Translation\trans('params.rechargeDiamond');
                break;
            default:
                $msg = \Hyperf\Translation\trans('params.rechargeDiamond');
        }
        Db::table('recharge_order')->where('id', $info['id'])->update($save);
        User::score($info['diamond'], $info['user_id'], $msg, 3);
        if ($pay_type != '4') {
            $if = Db::table('user')->where('id', $info['user_id'])->value('first_recharge');
            if ($if == '0') {
                Db::table('user')->where('id', $info['user_id'])->update(['first_recharge' => '1']);
            }
        }

        $prefix = $this->getRedisPrefix();
        $data = $this->redis->hmget($prefix . 'global_config', ['unlock_chat_num', 'unlock_weixin_num', 'unlock_chatup_vip', 'y_parent_rate', 'money_rate']);

        $total = $data['money_rate'] ? $money * 100 / $data['money_rate'] : 0;

        $add = [
            'action' => 'parent_get_money',
            'user_id' => $info['user_id'],
            'num' => $total,
            'type' => 'recharge'
        ];
        $this->nsq->publish('money', json_encode($add));
    }


    public function vip_buy_order($order_sn, $pay_type, $out_sn, $money)
    {
        $info = Db::table('vip_order')
            ->leftJoin('user as b', 'b.id', '=', 'vip_order.user_id')
            ->select('vip_order.*', 'b.p_user', 'b.gender', 'b.if_effective', 'b.is_vip')
            ->where('order_sn', $order_sn)
            ->first();
        if (empty($info)) {
            return;
        }
        if ($info['status'] != '0') {
            return;
        } else {
            $save = [
                'status'   => '1',
                'pay_type' => $pay_type,
                'pay_time' => time(),
                'out_sn'   => $out_sn
            ];
            Db::table('vip_order')->where('id', $info['id'])->update($save);
            $vip = Db::table('vip')->where('id', $info['vip_id'])->first();
            User::vip_update_user($info['user_id'], $info['month'], $info['unit'], $info['status'], $vip['translation'], $vip['status']);

            $prefix = $this->getRedisPrefix();
            $data = $this->redis->hmget($prefix . 'global_config', ['unlock_chat_num', 'unlock_weixin_num', 'unlock_chatup_vip', 'y_parent_rate', 'money_rate']);

            Db::table('user')->where('id', $info['user_id'])->update([
                'unlock_chat' => $data['unlock_chat_num'],
                'unlock_weixin' => $data['unlock_weixin_num'],
                'unlock_chatup' => $data['unlock_chatup_vip']
            ]);

            if ($info['p_user'] && $info['if_effective'] === '0') {
                Db::table('user')->where('id', $info['user_id'])->update(['if_effective' => '1']);
                Db::table('user')->where('id', $info['p_user'])->increment('son_num', 1);
            }

            $total = $data['money_rate'] ? $money * 100 / $data['money_rate'] : 0;
            $add = ['action' => 'parent_get_money', 'user_id' => $info['user_id'], 'num' => $total, 'type' => 'buy_vip'];
            $this->nsq->publish('money', json_encode($add));
        }
    }


    /**
     * 赠送天使币
     */
    public function send_score_num(RequestInterface $req): array
    {
        try {
            $user_id = $this->getCurrentUserId();
            $to_user_id = $req->input('to_user_id', 0);
            $price = $req->input('price', 0.00);

            $info = User::query()->where('id', $user_id)->select('score', 'from_score')->first();
            if (!$info) {
                throw new \Exception(\Hyperf\Translation\trans('params.userEmpty'));
            }

            $info = $info->toArray();
            if ($price > ($info['score'] + $info['from_score'])) {
                throw new \Exception(\Hyperf\Translation\trans('params.balanceError'));
            }

            $prefix = $this->getRedisPrefix();
            $data = $this->redis->hmget($prefix . 'global_config', ['score_unlock_day']);
            $end_time = time() + (86400 * $data['score_unlock_day']);

            $log_id = User::score(-$price, $user_id, \Hyperf\Translation\trans('params.sendScoreToUser'), 1, $end_time, 2, $to_user_id);
            User::score($price, $to_user_id, \Hyperf\Translation\trans('params.getScoreToUser'), 2, $end_time, 2, $user_id, $log_id);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }

        return success(\Hyperf\Translation\trans('params.success'), compact('log_id'));
    }


    /**
     * 投诉天使币
     */
    public function audit_score_log(RequestInterface $req): array
    {
        try {
            $user_id = $this->getCurrentUserId();
            $log_id = $req->input('log_id', '');

            if (!$log_id) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            $log_info = Db::table('user_score_log')->where('id', $log_id)->first();
            if (!$log_info) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            if ($log_info['is_ice'] != 2 || $log_info['is_jubao'] != 2) {
                throw new \Exception(\Hyperf\Translation\trans('params.scoreAuditError'));
            }

            $score = abs($log_info['score']);
            $add = [
                'user_id' => $user_id,
                'to_user_id' => $log_info['from_uid'],
                'user_score_log_id' => $log_id,
                'num' => $score,
                'createtime' => time(),
                'remark' => $req->input('remark', ''),
                'images' => $req->input('images', ''),
                'nickname' => $req->input('nickname', ''),
                'email' => $req->input('email', '')
            ];

            $audit_id = Db::table('user_score_audit')->insertGetId($add);
            Db::table('user_score_log')->whereIn('id', [$log_id])->orWhere('log_id', $log_id)->update(['is_jubao' => 1]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }

        return success('ok', compact('audit_id'));
    }


    /**
     * 用户积分变更记录
     */
    public function user_score_log(RequestInterface $request): array
    {
        try {
            $user_id = $this->getCurrentUserId();
            $month = $request->input('month', '');
            $log_id = $request->input('log_id', '');
            $type = $request->input('type', '0');

            $start = $end = null;
            if ($month) {
                $first = $month . '-01 00:00:00';
                $start = strtotime($first);
                $end = strtotime(date('Ymd 23:59:59', strtotime("$first +1 month -1 day")));
            }

            if ($type === '3') {
                $list = Db::table('user_score_audit')
                    ->join('user', 'user_score_audit.user_id', 'user.id')
                    ->select('user_score_audit.id as log_id', 'user_score_audit.num', 'user_score_audit.createtime', 'user_score_audit.remark', 'user.nickname', 'user.avatar', 'user_score_audit.user_id')
                    ->where(function ($sql) use ($user_id, $log_id) {
                        $sql->where('user_score_audit.user_id', $user_id)->orWhere('user_score_audit.to_user_id', $user_id);
                        if ($log_id) {
                            $sql->where('user_score_audit.id', $log_id);
                        }
                    })
                    ->when($month, function ($sql) use ($start, $end) {
                        $sql->whereBetween('user_score_audit.createtime', [$start, $end]);
                    })
                    ->orderBy('user_score_audit.createtime', 'desc')
                    ->get();
            } else {
                $list = Db::table('user_score_log')
                    ->join('user', 'user_score_log.user_id', 'user.id')
                    ->select('user_score_log.id as log_id', 'user_score_log.score', 'user_score_log.createtime', 'user_score_log.memo', 'user.nickname', 'user.avatar')
                    ->where(function ($sql) use ($user_id, $log_id, $type) {
                        $sql->where('user_score_log.user_id', $user_id);
                        if ($log_id) {
                            $sql->where('user_score_log.id', $log_id);
                        }
                        if ($type === '1') {
                            $sql->where('user_score_log.score', '>', 0);
                        } elseif ($type === '2') {
                            $sql->where('user_score_log.score', '<', 0);
                        }
                    })
                    ->when($month, function ($sql) use ($start, $end) {
                        $sql->whereBetween('user_score_log.createtime', [$start, $end]);
                    })
                    ->orderBy('user_score_log.createtime', 'desc')
                    ->get();
            }
        } catch (\Exception $e) {
            return $this->handleException($e);
        }

        return success('ok', compact('list'));
    }
}
