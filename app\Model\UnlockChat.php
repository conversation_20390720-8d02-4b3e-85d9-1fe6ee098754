<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\DbConnection\Model\Model;
use Hyperf\Di\Annotation\Inject;

/**
 * @property int $id
 * @property int $from_id
 * @property int $to_id
 * @property int $createtime
 */
class UnlockChat extends Model
{
    protected ?string $table = 'unlock_chat';
    protected array $fillable = [];
    protected array $casts = [
        'id' => 'integer',
        'from_id' => 'integer',
        'to_id' => 'integer',
        'createtime' => 'integer'
    ];
    public bool $timestamps = false;

    #[Inject]
    protected \Hyperf\Redis\Redis $redis;

    /**
     * 解锁我的列表
     */
    public static function unlock_me_list($user_id, $page, $limit)
    {
        try {
            $offset = ($page - 1) * $limit;
            $users = self::query()
                ->leftJoin('user', 'user.id', '=', 'unlock_chat.from_id')
                ->where('to_id', $user_id)
                ->orderBy('unlock_chat.createtime', 'desc')
                ->offset($offset)
                ->limit($limit)
                ->select([
                    'from_id as user_id',
                    'unlock_chat.createtime',
                    'forever_on',
                    'nickname',
                    'avatar',
                    'gender',
                    'is_auth',
                    'girl_switch',
                    'is_vip'
                ])
                ->get();

            if ($users->isEmpty()) {
                $result = [];
            } else {
                $result = self::processUserList($users->toArray(), $user_id);
            }
        } catch (\Exception $e) {
            $result = [];
        }

        return $result;
    }

    /**
     * 我解锁的人员列表
     */
    public static function my_unlock_list($user_id, $page, $limit)
    {
        try {
            $offset = ($page - 1) * $limit;
            $users = self::query()
                ->leftJoin('user', 'user.id', '=', 'unlock_chat.to_id')
                ->where('from_id', $user_id)
                ->orderBy('unlock_chat.createtime', 'desc')
                ->offset($offset)
                ->limit($limit)
                ->select([
                    'to_id as user_id',
                    'forever_on',
                    'unlock_chat.createtime',
                    'nickname',
                    'avatar',
                    'gender',
                    'is_auth',
                    'girl_switch',
                    'is_vip'
                ])
                ->get();

            if ($users->isEmpty()) {
                $result = [];
            } else {
                $result = self::processUserList($users->toArray(), $user_id);
            }
        } catch (\Exception $e) {
            $result = [];
        }

        return $result;
    }

    /**
     * 处理用户列表数据
     */
    private static function processUserList(array $list, $user_id): array
    {
        if (empty($list)) {
            return [];
        }

        $container = \Hyperf\Context\ApplicationContext::getContainer();
        $config = $container->get(\Hyperf\Contract\ConfigInterface::class);
        $prefix = $config->get('redis.default.prefix', '');
        $redis = $container->get(\Hyperf\Redis\Redis::class);

        foreach ($list as &$user) {
            $user['avatar'] = get_avatar($user['avatar']);
            $user['createtime_attr'] = last_time($user['createtime']);

            $alias_name = $redis->hget("{$prefix}alias_user_{$user_id}", (string)$user['user_id']);
            $user['alias_name'] = $alias_name ?: '';
        }
 
        return $list;
    }
}
