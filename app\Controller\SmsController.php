<?php

declare(strict_types=1);

namespace App\Controller;

use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Contract\RequestInterface;
use App\Model\User;
use App\Model\Sms as Smslib;
use Hyperf\Utils\ApplicationContext;



/**
 * 手机短信接口
 */
#[AutoController]
class SmsController extends AbstractController
{


    function getRealIp(): string
    {
        $request = ApplicationContext::getContainer()->get(RequestInterface::class);
        $headers = $request->getHeaders();

        if (isset($headers['x-forwarded-for'][0]) && !empty($headers['x-forwarded-for'][0])) {
            return $headers['x-forwarded-for'][0];
        } elseif (isset($headers['x-real-ip'][0]) && !empty($headers['x-real-ip'][0])) {
            return $headers['x-real-ip'][0];
        }

        $serverParams = $request->getServerParams();
        return $serverParams['remote_addr'] ?? '';
    }



    /**
     * 发送验证码
     *
     * @ApiMethod (POST)
     * @param string $mobile 手机号
     * @param string $event 事件名称
     */
    public function send(RequestInterface $req)
    {
        $mobile = $req->input("mobile", '');
        $event = $req->input("event", 'register');
        $ip = $this->getRealIp();

        if (!$mobile || !preg_match("/^1\d{10}$/", $mobile)) {
            return error('手机号不正确');
        }
        $last = Smslib::get($mobile, $event);
        if ($last && time() - $last['createtime'] < 60) {
            return error('发送频繁');
        }
        $ipSendTotal = Smslib::query()->where(['ip' => $ip])->whereTime('createtime', '>', date('H:i:s', strtotime('-1 hours')))->count();
        if ($ipSendTotal >= 5) {
            return error('发送频繁');
        }
        if ($event) {
            $userinfo = User::query()->where('mobile', $mobile)->first();
            if (($event == 'register') && $userinfo) { //|| $event ==
                //已被注册
                return error('已被注册');
            } elseif (in_array($event, ['changemobile']) && $userinfo) {
                //被占用
                return error('已被占用');
            } elseif (in_array($event, ['changepwd', 'resetpwd']) && !$userinfo) {
                //未注册
                return error('请先注册');
            } elseif (in_array($event, ['check']) && !$userinfo) {
                return error('原手机号不正确');
            }
        }
        $url = env('uri') . 'api/sms/send?mobile=' . $mobile . '&event=' . $event;
        $res = file_get_contents($url);
        $res = json_decode($res, true);
        if ($res['code'] == '1') {
            return success('发送成功');
        } else {
            return error('发送失败');
        }
    }

    /**
     * 检测验证码
     *
     * @ApiMethod (POST)
     * @param string $mobile 手机号
     * @param string $event 事件名称
     * @param string $captcha 验证码
     */
    public function check(RequestInterface $req)
    {
        $mobile = $req->input("mobile");
        $event = $req->input("event");
        $event = $event ? $event : 'register';
        $captcha = $req->input("captcha");

        if (!$mobile || !preg_match("/^1\d{10}$/", $mobile)) {
            return error('手机号不正确');
        }
        if ($event) {
            $userinfo = User::query()->where('mobile', $mobile)->first();
            if ($event == 'register' && $userinfo) {
                //已被注册
                return error('已被注册');
            } elseif (in_array($event, ['changemobile']) && $userinfo) {
                //被占用
                return error('已被占用');
            } elseif (in_array($event, ['changepwd', 'resetpwd']) && !$userinfo) {
                //未注册
                return error('未注册');
            }
        }
        $ret = Smslib::check($mobile, $captcha, $event);
        if ($ret) {
            return success('成功');
        } else {
            return error('验证码不正确');
        }
    }
}
