<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\DbConnection\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Utils\ApplicationContext;

class User extends Model
{
    public bool $timestamps = false;
    protected ?string $table = 'user';
    protected array $fillable = ['username', 'nickname', 'password', 'salt', 'email', 'mobile', 'avatar', 'level', 'gender', 'prevtime', 'logintime', 'loginip', 'joinip', 'jointime', 'createtime', 'updatetime', 'status'];
    protected array $casts = ['id' => 'integer', 'group_id' => 'integer', 'level' => 'integer', 'gender' => 'integer', 'score' => 'integer', 'successions' => 'integer', 'maxsuccessions' => 'integer', 'prevtime' => 'integer', 'logintime' => 'integer', 'loginfailure' => 'integer', 'jointime' => 'integer', 'createtime' => 'integer', 'updatetime' => 'integer', 'height' => 'integer', 'weight' => 'integer', 'vip_end_time' => 'integer', 'girl_switch' => 'integer', 'p_user' => 'integer', 'fans_num' => 'integer', 'follow_num' => 'integer', 'visit_num' => 'integer', 'dynamic_num' => 'integer'];

    private const USER_SELECT_FIELDS = [
        'point_city', 'play_city', 'master_city', 'weights', 'id as user_id', 'country', 'vip_type',
        'nickname', 'avatar', 'gender', 'girl_switch', 'birthday', 'height', 'weight', 'master_city',
        'point_city', 'is_vip', 'is_auth', 'on_line', 'occupation', 'nearby_hide', 'distance_hide',
        'lng', 'lat', 'forever_on', 'is_ls'
    ];

    public function user_list($user_id, $page, $limit, $key_word, $hot_top, $nearby, $lng, $lat, $city_name, $on_line, $girl_switch, $have_auth, $brand_new, $age_between, $is_vip, $gender, $find_sex, $point_city, $point_province, $height, $weights, $language, $revenue, $occupation, $relation, $time, $car_budget, $is_real, $country, $lang = 'zh-cn')
    {
        $params = [
            'user_id' => $user_id, 'page' => $page, 'limit' => $limit, 'key_word' => $key_word,
            'hot_top' => $hot_top, 'nearby' => $nearby, 'lng' => $lng ?: '0.0', 'lat' => $lat ?: '0.0',
            'city_name' => $city_name, 'on_line' => $on_line, 'girl_switch' => $girl_switch,
            'have_auth' => $have_auth, 'brand_new' => $brand_new, 'age_between' => $age_between,
            'is_vip' => $is_vip, 'gender' => $gender, 'find_sex' => $find_sex, 'point_city' => $point_city,
            'point_province' => $point_province, 'height' => $height, 'weights' => $weights,
            'language' => $language, 'revenue' => $revenue, 'occupation' => $occupation,
            'relation' => $relation, 'time' => $time, 'car_budget' => $car_budget,
            'is_real' => $is_real, 'country' => $country, 'lang' => $lang,
            'new_str' => ($pos = strrpos($point_city, '市')) ? substr($point_city, 0, $pos) : $point_city
        ];

        $conditions = $this->buildQueryConditions($params);
        $redis = ApplicationContext::getContainer()->get(\Hyperf\Redis\Redis::class);
        $prefix = env('redis_prefix');
        $ls_switch = $redis->hmget($prefix . 'global_config', ['ls_switch'])['ls_switch'];
        $result = $this->queryUserList($conditions, $params, $ls_switch);

        return $this->processUserData($result, $params, $redis);
    }

    private function buildQueryConditions($params)
    {
        $where = $wheres = $search_where = [];
        $wheres[] = ['is_ls', '=', 1];

        if ($params['is_real']) $wheres['id'] = 0;
        if ($params['nearby'] == '1' && $params['point_city']) $wheres[] = ['point_city', 'LIKE', "%{$params['new_str']}%"];
        if ($params['city_name']) $wheres[] = ['point_city', 'LIKE', "%{$params['city_name']}%"];

        $hasSearchConditions = $params['key_word'] || $params['city_name'] || $params['on_line'] || $params['have_auth'] || $params['age_between'];

        if (!$params['find_sex'] && !$params['age_between']) {
            if ($params['gender'] == 2) {
                $where[] = $search_where[] = $wheres[] = ['gender', '=', 2];
                if ($params['girl_switch'] == '1' || (!$hasSearchConditions && $params['girl_switch'] != '1')) {
                    $condition = ['girl_switch', '=', $params['girl_switch'] == '1' ? 1 : 0];
                    $where[] = $wheres[] = $condition;
                }
            } elseif ($params['gender'] == 1) {
                $where[] = $wheres[] = $search_where[] = ['gender', '=', 1];
                if ($params['is_vip'] == '1' || (!$hasSearchConditions && $params['is_vip'] != '1')) {
                    $where[] = ['is_vip', '=', $params['is_vip'] == '1' ? 'one' : 'three'];
                }
            }
        } else {
            if ($params['find_sex'] == '1') {
                $where[] = $wheres[] = ['gender', '=', '1'];
                $search_where[] = ['gender', '=', 1];
            } elseif ($params['find_sex'] == '2') {
                $where[] = $wheres[] = ['gender', '=', '2'];
                $search_where[] = ['gender', '=', 2];
            }
        }

        return compact('where', 'wheres', 'search_where');
    }

    private function queryUserList($conditions, $params, $ls_switch)
    {
        if (!$params['key_word']) {
            return $ls_switch == '1'
                ? $this->queryAndMergeUsers($conditions, $params)
                : $this->queryNormalUsersWithPagination($conditions['where'], $params);
        }

        return $params['page'] > 1 ? [] : $this->querySearchUsers($conditions['search_where'], $params);
    }

    private function queryAndMergeUsers($conditions, $params)
    {
        $totalNeeded = $params['page'] * $params['limit'] + 20;
        $ls_list = $this->buildUserQuery($conditions['wheres'], $params, $totalNeeded, false);
        $normal_list = $this->buildUserQuery($conditions['where'], $params, $totalNeeded, true);
        $merged_list = $this->mergeAndDeduplicateUsers($normal_list, $ls_list);
        return array_slice($merged_list, ($params['page'] - 1) * $params['limit'], $params['limit']);
    }

    private function buildUserQuery($where, $params, $limit, $isNormal = false)
    {
        $query = self::query();

        if ($isNormal) {
            $query->where('nearby_hide', '1')
                  ->where('have_exit', 0)
                  ->where('is_ls', '0')
                  ->where('status', 'normal')
                  ->whereNotIn('id', function ($sql) use ($params) {
                      $sql->from('forbid_user')->where('from_id', $params['user_id'])->select('to_id');
                  });
        }

        return $query->where($where)
            ->select(self::USER_SELECT_FIELDS)
            ->addSelect(Db::raw($this->getDistanceSelectRaw($params['lat'], $params['lng'])))
            ->when($params['key_word'], fn($sql) => $this->addKeywordCondition($sql, $params['key_word']))
            ->when($params['hot_top'] == '1', fn($sql) => $sql->where('point_province', $params['point_province'])->where('girl_switch', 0))
            ->when($params['nearby'] == '1' && $params['new_str'], fn($sql) => $sql->where('point_city', 'LIKE', "%{$params['new_str']}%"))
            ->when($params['city_name'], fn($sql) => $sql->where('point_city', $params['city_name']))
            ->when($params['on_line'] == '1', fn($sql) => $sql->where('on_line', '1'))
            ->when($params['have_auth'] == '1', fn($sql) => $sql->where('is_auth', '1'))
            ->when($params['gender'] == 2, fn($sql) => $sql->inRandomOrder())
            ->when($params['age_between'], fn($sql) => $this->addAgeCondition($sql, $params['age_between'], $params['gender']))
            ->when($params['height'], fn($sql) => $this->addHeightCondition($sql, $params['height']))
            ->when($params['weights'], fn($sql) => $sql->where('weights', $params['weights']))
            ->when($params['language'], fn($sql) => $sql->whereRaw('FIND_IN_SET(' . $params['language'] . ',language)'))
            ->when($params['revenue'], fn($sql) => $sql->where('revenue', $params['revenue']))
            ->when($params['occupation'], fn($sql) => $sql->where('occupation', $params['occupation']))
            ->when($params['relation'], fn($sql) => $sql->whereRaw('FIND_IN_SET(' . $params['relation'] . ',relation)'))
            ->when($params['time'], fn($sql) => $sql->whereRaw('FIND_IN_SET(' . $params['time'] . ',time)'))
            ->when($params['car_budget'], fn($sql) => $sql->whereRaw('FIND_IN_SET(' . $params['car_budget'] . ',car_budget)'))
            ->when($params['country'], fn($sql) => $sql->where('country', $params['country']))
            ->groupBy('id')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    private function queryNormalUsersWithPagination($where, $params)
    {
        return $this->buildUserQuery($where, $params, $params['limit'], true)
            ->offset(($params['page'] - 1) * $params['limit'])
            ->get()
            ->toArray();
    }

    private function querySearchUsers($search_where, $params)
    {
        return self::query()
            ->where('have_exit', 0)
            ->where('status', 'normal')
            ->whereNotIn('id', function ($sql) use ($params) {
                $sql->from('forbid_user')->where('from_id', $params['user_id'])->select('to_id');
            })
            ->select(self::USER_SELECT_FIELDS)
            ->addSelect(Db::raw($this->getDistanceSelectRaw($params['lat'], $params['lng'])))
            ->when($params['key_word'], fn($sql) => $this->addKeywordCondition($sql, $params['key_word']))
            ->where($search_where)
            ->groupBy('id')
            ->orderBy('distance', 'asc')
            ->get()
            ->toArray();
    }

    private function mergeAndDeduplicateUsers($normal_list, $ls_list)
    {
        $all_users = array_merge($normal_list, $ls_list);
        $seen = [];
        $ls_users = $normal_users = [];

        foreach ($all_users as $user) {
            $userId = $user['user_id'];
            if (!isset($seen[$userId])) {
                $seen[$userId] = true;
                if (isset($user['is_ls']) && $user['is_ls'] == 1) {
                    $ls_users[] = $user;
                } else {
                    $normal_users[] = $user;
                }
            }
        }

        $result = [];
        $max_count = max(count($ls_users), count($normal_users));
        for ($i = 0; $i < $max_count; $i++) {
            if (isset($normal_users[$i])) $result[] = $normal_users[$i];
            if (isset($ls_users[$i])) $result[] = $ls_users[$i];
        }

        return $result;
    }

    private function processUserData($list, $params, $redis)
    {
        if (empty($list)) return [];

        $seen = [];
        $unique_list = [];
        foreach ($list as $user) {
            if (!isset($seen[$user['user_id']])) {
                $seen[$user['user_id']] = true;
                $unique_list[] = $user;
            }
        }

        $uids = array_column($unique_list, 'user_id');
        $photoStats = $this->getUserPhotoStats($uids);
        $prefix = env('redis_prefix');
        $occupation = $redis->hgetall($prefix . 'global_select_code');

        foreach ($unique_list as $x => &$y) {
            $this->processUserItem($y, $x, $unique_list, $params, $redis, $prefix, $occupation, $photoStats);
        }

        return array_filter($unique_list);
    }

    private function processUserItem(&$user, $index, &$list, $params, $redis, $prefix, $occupation, $photoStats)
    {
        $alias_name = $redis->hget("{$prefix}alias_user_{$params['user_id']}", "{$user['user_id']}");
        $list[$index]['alias_name'] = $alias_name === false ? '' : $alias_name;
        $list[$index]['avatar'] = get_avatar($user['avatar']);

        $lang_str = choose_lang($params['lang']);
        $field = $params['lang'] == 'zh-cn' ? 'code' : $lang_str . '_code';
        $weights = Db::table('select_resources')->where('id', $user['weights'])->select($field)->first();
        $user['weights'] = $weights[$field] ?? '';

        $user['country'] = $user['country'] ?? '';
        $user['master_city'] = $user['master_city'] ?? '';
        $user['point_city'] = $user['point_city'] ?? '';

        if (!$user['occupation']) {
            $user['occupation'] = $user['occupation_arr'] = '';
        } else {
            $user['occupation_arr'] = strtr($user['occupation'], $occupation);
        }

        if (!$user['birthday']) {
            $list[$index]['age'] = 0;
            $user['birthday'] = '';
        } else {
            $user['age'] = get_age($user['birthday']);
        }

        $user['height'] = $user['height'] ?: '';
        $user['weight'] = $user['weight'] ?: '';
        $user['lng'] = $user['lng'] ?: '';
        $user['lat'] = $user['lat'] ?: '';
        $user['distance'] = $user['distance'] ?? '';

        if ($user['forever_on'] == 1) {
            $nums = rand(0, 1500) / 100;
            $list[$index]['distance_str'] = sprintf("%.2f", $nums) . 'km';
            $user['point_city'] = $params['point_city'];
        } else {
            $list[$index]['distance_str'] = $user['distance'] === null ? '无' :
                ($user['distance_hide'] == '1' ? sprintf("%.2f", $user['distance'] / 1000) . ' km' : '保密');
        }

        $on_line = $redis->get($prefix . 'on_line_' . $user['user_id']);
        if (!$on_line) {
            $global_online = $redis->hget($prefix . 'global_online', strval($user['user_id']));
            if (!$global_online) {
                $userInfo = self::query()->where('id', $user['user_id'])->first();
                $global_online = ($userInfo && $userInfo['online_time']) ? $userInfo['online_time'] : time();
                $redis->hset($prefix . 'global_online', strval($user['user_id']), strval($global_online));
            }
            $user['on_line'] = last_time($global_online);
            $redis->setex($prefix . 'on_line_' . $user['user_id'], 180, 1);
        } else {
            $user['on_line'] = \Hyperf\Translation\trans('params.online');
        }

        if ($params['gender'] == 2) {
            $user['on_line'] = \Hyperf\Translation\trans('params.online');
        }

        $user['video_count'] = $user['image_count'] = 0;
        foreach ($photoStats as $stat) {
            if ($stat['user_id'] == $user['user_id']) {
                $user[$stat['file_type'] == '1' ? 'image_count' : 'video_count'] = $stat['num'];
            }
        }
    }

    private function getDistanceSelectRaw($lat, $lng)
    {
        return "ROUND(6378.138*2*ASIN(SQRT(POW(SIN(({$lat}*PI()/180-lat*PI()/180)/2),2)+COS({$lat}*PI()/180)*COS(lat*PI()/180)*POW(SIN(({$lng}*PI()/180-lng*PI()/180)/2),2)))*1000) AS distance";
    }

    private function addKeywordCondition($query, $keyword)
    {
        $query->where('nickname', 'like', '%' . $keyword . '%')
            ->orWhere(function ($sql2) use ($keyword) {
                $sql2->when(is_num($keyword), function ($sql3) use ($keyword) {
                    $sql3->where('id', 'like', '%' . $keyword . '%');
                });
            });
    }

    private function addHeightCondition($query, $height)
    {
        $li = explode(',', $height);
        $query->whereBetween('height', [$li[0] ?? 140, $li[1] ?? 200]);
    }

    private function addAgeCondition($query, $age_between, $gender)
    {
        $li = explode(',', $age_between);
        $now = time();
        foreach ($li as $x => $y) {
            $li[$x] = strtotime('- ' . $y . ' years', $now);
        }
        sort($li);
        $query->addSelect(Db::raw('unix_timestamp(birthday) as birthday_time'))
            ->havingBetween('birthday_time', $li)
            ->where('gender', $gender);
    }

    private function getUserPhotoStats($uids)
    {
        return Db::table('photo')
            ->whereIn('user_id', $uids)
            ->groupBy('user_id', 'file_type')
            ->select(Db::raw('count(id) as num,user_id,file_type'))
            ->get()
            ->toArray();
    }


    public static function vip_update_user($user_id, $month, $unit, $trans, $vip_type)
    {
        $user = self::query()->select('id', 'vip_end_time', 'is_vip', 'vip_type')->find($user_id);

        if ($user->is_vip == 'two') return;

        $fir = $user->vip_end_time <= time() ? time() : $user->vip_end_time;

        if ($month >= 1200 && $unit == 'month') {
            $user->vip_end_time = 0;
        } else {
            $end_time = $unit == 'month'
                ? strtotime('+' . $month . ' month', $fir)
                : strtotime('+' . $month . ' day', $fir);
            $user->vip_end_time = $end_time;
            $user->translation_num += $trans;
        }

        $user->vip_type = $vip_type;
        $user->is_vip = 'one';
        $user->save();
    }

    public static function money($money, $user_id, $memo, $status = '1')
    {
        $user = self::query()->where('id', $user_id)->select('id', 'money')->first();

        if ($user && bccomp(strval($money), '0', 2) !== 0) {
            $before = $user->money;
            $after = bcadd($user->money, $money, 2);
            self::query()->where('id', $user_id)->update(['money' => $after]);

            Db::table('user_money_log')->insert([
                'user_id' => $user_id, 'money' => $money, 'before' => $before,
                'after' => $after, 'memo' => $memo, 'status' => $status, 'createtime' => time()
            ]);
        }
    }

    public static function score($score, $user_id, $memo, $type = 1, $endtime = 0, $is_jubao = 2, $from_uid = 0, $log_id = 0)
    {
        $user = self::query()->where('id', $user_id)
            ->select('id', 'score', 'from_score', 'ice_score')
            ->first();

        if (!$user || $score == 0) return false;

        $before = $after = 0;

        switch ($type) {
            case 1:
                $cha = $user->from_score + $score;
                if ($cha > 0) {
                    $from_score = $cha;
                    $after_score = $user->score;
                } else {
                    $from_score = 0;
                    $after_score = $user->score + $cha;
                }
                self::query()->where('id', $user_id)->update(['score' => $after_score, 'from_score' => $from_score]);
                break;
            case 2:
                $before = $user->ice_score;
                $after = $user->ice_score + $score;
                self::query()->where('id', $user_id)->update(['ice_score' => $after]);
                break;
            case 3:
                $before = $user->score;
                $after = $user->score + $score;
                self::query()->where('id', $user_id)->update(['score' => $after]);
                break;
        }

        return Db::table('user_score_log')->insertGetId([
            'user_id' => $user_id, 'score' => $score, 'before' => $before, 'after' => $after,
            'memo' => $memo, 'createtime' => time(), 'type' => $type, 'endtime' => $endtime,
            'is_jubao' => $is_jubao, 'from_uid' => $from_uid, 'log_id' => $log_id, 'is_ice' => 2
        ]);
    }

    public static function getUserId()
    {
        do {
            $num = mt_rand(10000, 99999);
        } while (self::query()->where('id', $num)->exists());

        return $num;
    }

    public function getAvatarAttribute($val)
    {
        return get_avatar($val);
    }

}
