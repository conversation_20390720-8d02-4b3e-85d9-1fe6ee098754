<?php

namespace App\Controller;

use App\Middleware\Auth\TokenMiddleware;
use App\Model\User;
use Carbon\Carbon;
use Hyperf\Context\Context;
use Hyperf\Coroutine\Locker;
use Hyperf\Database\Model\Builder;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middlewares;
use Hyperf\Redis\Redis;

#[AutoController]
#[Middlewares([TokenMiddleware::class])]
class PairingController extends AbstractController
{
    #[Inject]
    private Redis $redis;

    private const MATCH_TYPES = [
        0 => "同城",
        1 => "有相同的特点",
        2 => "期待的关系",
        3 => "相同的职业",
        4 => "TA和你有这相同的爱好",
        5 => "TA和你年龄相仿",
        6 => "TA和你曾经擦肩而过",
    ];

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix', '');
    }

    /**
     * 获取当前用户ID并验证登录状态
     */
    private function getCurrentUserId(): int
    {
        $user_id = Context::get('user_id', 0);
        if (empty($user_id)) {
            throw new \Exception(\Hyperf\Translation\trans('params.loginError'), 10001);
        }
        return (int)$user_id;
    }

    /**
     * 获取配置价格
     */
    private function getConfigPrice(string $key): float
    {
        $prefix = $this->getRedisPrefix();
        return (float)$this->redis->hGet($prefix . 'global_config', $key);
    }

    /**
     * 检查用户余额并扣费
     */
    private function checkAndDeductBalance(int $user_id, float $price, string $description): void
    {
        $my_money = Db::table('user')->where('id', $user_id)->value('money');
        if ($my_money < $price) {
            throw new \Exception(\Hyperf\Translation\trans('params.balanceError'), 1102);
        }
        User::score('-' . $price, $user_id, $description);
    }

    /**
     * 获取匹配条件
     */
    private function getMatchCondition(int $rand, User $user): \Closure
    {
        switch ($rand) {
            case 0:
                return function (Builder $builder) use ($user) {
                    $builder->where("point_city", $user->point_city);
                };
            case 1:
                return function (Builder $builder) use ($user) {
                    if (!$user->characters) return;
                    $conditions = array_map(function ($v) {
                        return "find_in_set(`label`, '{$v}')";
                    }, explode(",", $user->characters));
                    $builder->whereRaw(implode(" or ", $conditions));
                };
            case 2:
                return function (Builder $builder) use ($user) {
                    $builder->where("relation", $user->relation);
                };
            case 3:
                return function (Builder $builder) use ($user) {
                    $builder->where("occupation", $user->occupation);
                };
            case 4:
                return function (Builder $builder) use ($user) {
                    if (!$user->label) return;
                    $conditions = array_map(function ($v) {
                        return "find_in_set(`label`, '{$v}')";
                    }, explode(",", $user->label));
                    $builder->whereRaw(implode(" or ", $conditions));
                };
            case 5:
                return function (Builder $builder) use ($user) {
                    $builder->where("birthday", ">=", Carbon::createFromFormat("Y-m-d", $user->birthday)->subYears(3))
                        ->where("birthday", "<=", Carbon::createFromFormat("Y-m-d", $user->birthday)->addYears(3));
                };
            case 6:
                return function (Builder $builder) use ($user) {
                    if (!$user->pint_city) return;
                    $conditions = array_map(function ($v) {
                        return "find_in_set(`pint_city`, '{$v}')";
                    }, explode(",", $user->pint_city));
                    $builder->whereRaw(implode(" or ", $conditions));
                };
            default:
                return function () {};
        }
    }

    /**
     * 头像示例
     */
    public function avatar_example()
    {
        $gender = $this->request->post("gender", 0);
        $prefix = $this->getRedisPrefix();

        $config_key = null;
        if ($gender == 1) {
            $config_key = 'register_avatar_examples_x';
        } elseif ($gender == 2) {
            $config_key = 'register_avatar_examples_y';
        }

        if (!$config_key) {
            return success("", []);
        }

        $avatars = (string)$this->redis->hGet($prefix . 'global_config', $config_key);
        $list = array_map(fn($v) => get_avatar($v), explode(",", $avatars));

        return success("", $list);
    }

    /**
     * 查询用户匹配次数
     */
    public function user_times_show()
    {
        try {
            $user_id = $this->getCurrentUserId();
            $user = User::query()->find($user_id);
            $data = $user->setVisible(["id", "pair_voice_leave", "pair_live_leave"])->toArray();
            $data["pair_voice_price"] = $this->getConfigPrice("pair_voice_price");
            $data["pair_live_price"] = $this->getConfigPrice("pair_live_price");
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success("", $data);
    }

    /**
     * 获取一批用户
     */
    public function get_user_rand()
    {
        try {
            $this->getCurrentUserId();
            $limit = $this->request->post("limit", 50);
            $list = User::query()
                ->where("on_line", 1)
                ->where('have_exit', 0)
                ->selectRaw("id,avatar,nickname,gender")
                ->orderByRaw("rand()")
                ->limit($limit)
                ->get();
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success("", $list);
    }

    /**
     * 智能匹配
     */
    public function common_find()
    {
        try {
            $user_id = $this->getCurrentUserId();
            $sex = $this->request->post("sex", null);
            $city = $this->request->post("city", null);
            $age_min = $this->request->post("age_min", null);
            $age_max = $this->request->post("age_max", null);

            $user = User::query()->find($user_id);
            $rand = array_rand(self::MATCH_TYPES);
            $randWhere = $this->getMatchCondition($rand, $user);

            $who = User::query()
                ->where($this->filter_where($sex, $city, $age_min, $age_max))
                ->where($randWhere)
                ->where('have_exit', 0)
                ->orderByRaw("rand()")
                ->selectRaw("id,avatar,nickname,gender,point_city,birthday,height")
                ->first();

            if (empty($who)) {
                throw new \Exception(\Hyperf\Translation\trans('params.matchingError'));
            }
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success("", ["user" => $who, "rand" => $rand]);
    }

    /**
     * 语音匹配
     */
    public function voice_find()
    {
        try {
            $user_id = $this->getCurrentUserId();
            $sex = $this->request->post("sex", null);
            $city = $this->request->post("city", null);
            $age_min = $this->request->post("age_min", null);
            $age_max = $this->request->post("age_max", null);

            if (!Locker::lock("voice_find_{$user_id}")) {
                throw new \Exception(\Hyperf\Translation\trans('params.requestError'));
            }

            $user = User::query()->find($user_id);

            if ($user->pair_voice_leave <= 0) {
                $money = $this->getConfigPrice("pair_voice_price");
                $this->checkAndDeductBalance($user_id, $money, \Hyperf\Translation\trans('params.voiceMatch'));
            }

            if ($user->is_vip != "one" && !empty($sex) && !empty($city) && !empty($age_min) && !empty($age_max)) {
                throw new \Exception(\Hyperf\Translation\trans('params.vipError'), 1102);
            }

            $who = User::query()
                ->where($this->filter_where($sex, $city, $age_min, $age_max))
                ->where('have_exit', 0)
                ->selectRaw("id,avatar,nickname,gender,point_city,birthday,height")
                ->orderByRaw("rand()")
                ->first();

            if (empty($who)) {
                throw new \Exception(\Hyperf\Translation\trans('params.matchingError'), 1102);
            }

            Locker::unlock("voice_find_{$user_id}");
        } catch (\Exception $e) {
            Locker::unlock("voice_find_{$user_id}");
            if ($e->getCode() == 1102) {
                return error($e->getMessage());
            }
            return error(\Hyperf\Translation\trans('params.error'));
        }

        return success("", ["user" => $who]);
    }

    /**
     * 视频匹配
     */
    public function video_find()
    {
        try {
            $user_id = $this->getCurrentUserId();
            $sex = $this->request->post("sex", null);
            $city = $this->request->post("city", null);
            $age_min = $this->request->post("age_min", null);
            $age_max = $this->request->post("age_max", null);

            if (!Locker::lock("video_find_{$user_id}")) {
                throw new \Exception(\Hyperf\Translation\trans('params.requestError'));
            }

            $user = User::query()->find($user_id);

            if ($user->pair_live_leave <= 0) {
                $money = $this->getConfigPrice("pair_live_price");
                $this->checkAndDeductBalance($user_id, $money, \Hyperf\Translation\trans('params.videoMatch'));
            }

            if ($user->is_vip != "one" && !empty($sex) && !empty($city) && !empty($age_min) && !empty($age_max)) {
                throw new \Exception(\Hyperf\Translation\trans('params.vipError'), 1102);
            }

            $who = User::query()
                ->where($this->filter_where($sex, $city, $age_min, $age_max))
                ->where('have_exit', 0)
                ->selectRaw("id,avatar,nickname,gender,point_city,birthday,height")
                ->orderByRaw("rand()")
                ->first();

            if (empty($who)) {
                throw new \Exception(\Hyperf\Translation\trans('params.matchingError'), 1102);
            }

            $user->decrement("pair_live_leave");
            Locker::unlock("video_find_{$user_id}");
        } catch (\Exception $e) {
            Locker::unlock("video_find_{$user_id}");
            if ($e->getCode() == 1102) {
                return error($e->getMessage());
            }
            return error(\Hyperf\Translation\trans('params.error'));
        }

        return success("", ["user" => $who]);
    }

    /**
     * 构建用户筛选条件
     */
    protected function filter_where($gender, $city, $age_min, $age_max): \Closure
    {
        return function (Builder $builder) use ($gender, $city, $age_min, $age_max) {
            if (in_array($gender, [1, 2])) {
                $builder->where("gender", $gender);
            }
            if ($city) {
                $builder->where("point_city", $city);
            }
            if ($age_min && is_numeric($age_min)) {
                $builder->where("birthday", "<=", Carbon::now()->subYears($age_min)->format("Y-m-d"));
            }
            if ($age_max && is_numeric($age_max)) {
                $builder->where("birthday", ">=", Carbon::now()->subYears($age_max)->format("Y-m-d"));
            }
        };
    }
}