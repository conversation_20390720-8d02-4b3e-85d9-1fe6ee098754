<?php

declare(strict_types=1);

namespace App\Nsq\Consumer;

use <PERSON>uz<PERSON><PERSON>ttp\Client;
use Guz<PERSON><PERSON>ttp\HandlerStack;
use Hyperf\Guzzle\CoroutineHandler;
use Hyperf\Nsq\AbstractConsumer;
use Hyperf\Nsq\Annotation\Consumer;
use Hyperf\Nsq\Message;
use Hyperf\Nsq\Result;
use Hyperf\Redis\RedisFactory;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;
use Hyperf\Nsq\Nsq;

#[Consumer(topic: 'dynamic', channel: 'tangx', name: 'DynamicConsumer', nums: 1)]
class DynamicConsumer extends AbstractConsumer
{
    private $redis;
    private $prefix;

    public function __construct()
    {
        $container = ApplicationContext::getContainer();
        $this->redis = $container->get(RedisFactory::class)->get('default');
        $this->prefix = env('redis_prefix');
    }

    public function isEnable(): bool
    {
        return true;
    }

    public function consume(Message $payload): ?string
    {
        $arr = json_decode($payload->getBody(), true);

        $actions = [
            'dynamic_add' => fn() => $this->dynamic_add($arr),
            'dynamic_great' => fn() => $this->dynamic_great($arr),
            'imgs_upload' => fn() => $this->imgs_upload($arr),
            'dynamic_cmt' => fn() => $this->dynamic_cmt($arr)
        ];

        if (isset($actions[$arr['action']])) {
            $actions[$arr['action']]();
        }

        return Result::ACK;
    }

    public function dynamic_add($arr)
    {
        $user_id = Db::table('user')->where('forever_on', '1')->inRandomOrder()->value('id');
        $imgs = $arr['imgs'] ?? '';
        $source_type = $imgs ? '1' : '0';
        $img_list = $imgs ? (strpos($imgs, ',') !== false ? explode(',', $imgs) : [$imgs]) : [];

        $dynamic_data = [
            'user_id' => $user_id,
            'content' => $arr['content'],
            'have_source' => $source_type,
            'location' => $arr['location'],
            'lat' => $arr['lat'],
            'lng' => $arr['lng'],
            'status' => '1',
            'createtime' => $arr['createtime']
        ];

        $dynamic_id = Db::table('dynamic')->insertGetId($dynamic_data);
        Db::table('user_base')->where('user_id', $user_id)->increment('dynamic_num', 1);

        if ($imgs) {
            $img_data = [];
            foreach ($img_list as $img) {
                if (@fopen(env('cdnurl') . $img, 'r')) {
                    $img_data[] = [
                        'dynamic_id' => $dynamic_id,
                        'user_id' => $user_id,
                        'image' => $img,
                        'source_type' => '1'
                    ];
                }
            }
            if ($img_data) {
                Db::table('dynamic_img')->insert($img_data);
            }
        }

        $config = $this->redis->hmget($this->prefix . 'global_config', ['global_dynamic_great', 'global_dynamic_cmt']);

        if ($config['global_dynamic_great']) {
            $this->redis->rpush($this->prefix . 'crontab_dynamic_great', strval($dynamic_id));
            $this->redis->setex($this->prefix . 'dynamic_greating_' . $dynamic_id, 86400, '1');
        }

        if ($config['global_dynamic_cmt']) {
            $this->redis->rpush($this->prefix . 'crontab_dynamic_cmt', strval($dynamic_id));
            $this->redis->setex($this->prefix . 'dynamic_cmting_' . $dynamic_id, 86400, '1');
        }
    }

    public function imgs_upload($arr)
    {
        $imgs = $arr['imgs'];

        $client = new Client([
            'base_uri' => env('uri') . 'api/index/imgs_upload?',
            'handler' => HandlerStack::create(new CoroutineHandler()),
            'timeout' => 5,
            'swoole' => [
                'timeout' => 5,
                'socket_buffer_size' => 1024 * 1024 * 2,
            ],
        ]);

        $client->get(env('uri') . 'api/index/imgs_upload?imgs=' . $imgs);
    }
    
    
    public function dynamic_great($arr)
    {
        $unix_time = $this->redis->lpop($this->prefix . 'dynamic_great_time_list_' . $arr['dynamic_id']);

        if ($unix_time !== false) {
            $container = ApplicationContext::getContainer();
            $nsq = $container->get(Nsq::class);
            $data = [
                'action' => 'dynamic_great',
                'dynamic_id' => $arr['dynamic_id'],
                'user_id' => $arr['user_id']
            ];
            $nsq->publish('dynamic', json_encode($data), $unix_time - time());
        }

        Db::table('dynamic')->where('id', $arr['dynamic_id'])->increment('great_num', 1);
        Db::table('user_base')->where('user_id', $arr['user_id'])->increment('great_num', 1);
    }
    public function dynamic_cmt($arr)
    {
        $unix_time = $this->redis->lpop($this->prefix . 'dynamic_cmt_time_list_' . $arr['dynamic_id']);

        if ($unix_time !== false) {
            $container = ApplicationContext::getContainer();
            $nsq = $container->get(Nsq::class);
            $data = [
                'action' => 'dynamic_cmt',
                'dynamic_id' => $arr['dynamic_id'],
                'user_id' => $arr['user_id'],
                'gender' => $arr['gender']
            ];
            $nsq->publish('dynamic', json_encode($data), $unix_time - time());
        }

        $user_id = Db::table('user')
            ->where('forever_on', '1')
            ->where('gender', $arr['gender'])
            ->inRandomOrder()
            ->value('id');

        $dynamic_id = $arr['dynamic_id'];
        $comments = $this->redis->srandmember($this->prefix . 'all_cmt_warehouse');

        if (!Db::table('dynamic')->where('id', $dynamic_id)->exists()) {
            return;
        }

        $comment_data = [
            'dynamic_id' => $dynamic_id,
            'user_id' => $user_id,
            'comments' => $comments,
            'createtime' => time()
        ];

        Db::table('dynamic_comments')->insert($comment_data);
        Db::table('dynamic')->where('id', $dynamic_id)->increment('comments_num', 1);

        $dynamic_user_id = Db::table('dynamic')->where('id', $dynamic_id)->value('user_id');
        if ($dynamic_user_id) {
            Db::table('user_base')->where('user_id', $dynamic_user_id)->increment('comment_num', 1);
        }
    }
}