<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\DbConnection\Model\Model;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

/**
 * @property int $id
 * @property int $from_id
 * @property int $to_id
 * @property int $createtime
 */
class ForbidUser extends Model
{
    public bool $timestamps = false;

    protected ?string $table = 'forbid_user';
    protected array $fillable = [];
    protected array $casts = [
        'id' => 'integer',
        'from_id' => 'integer',
        'to_id' => 'integer',
        'createtime' => 'integer'
    ];

    #[Inject]
    private Redis $redis;

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix', '');
    }

    /**
     * 获取用户别名
     */
    private function getUserAlias(int $user_id, int $target_user_id): string
    {
        $alias_name = $this->redis->hget($this->getRedisPrefix() . "alias_user_{$user_id}", (string)$target_user_id);
        return $alias_name === false ? '' : $alias_name;
    }

    /**
     * 处理用户信息
     */
    private function processUserInfo(array &$user, array $occupation_config, int $current_user_id): void
    {
        $user['occupation_arr'] = $user['occupation'] ? strtr($user['occupation'], $occupation_config) : '';
        $user['avatar'] = get_avatar($user['avatar']);
        $user['createtime_attr'] = last_time($user['createtime']);
        $user['age'] = $user['birthday'] ? get_age($user['birthday']) : 0;
        $user['alias_name'] = $this->getUserAlias($current_user_id, $user['user_id']);
    }

    /**
     * 获取拉黑用户列表
     */
    public function lahei_list($user_id, $page, $limit)
    {
        try {
            $occupation_config = $this->redis->hgetAll($this->getRedisPrefix() . 'global_select_code');

            $list = self::where('from_id', $user_id)
                ->leftJoin('user', 'user.id', '=', 'forbid_user.to_id')
                ->orderBy('createtime', 'desc')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->select('to_id as user_id', 'forbid_user.createtime', 'nickname', 'avatar', 'gender', 'is_auth', 'girl_switch', 'is_vip', 'height', 'weight', 'occupation', 'birthday')
                ->get();

            if (!$list) {
                $result = [];
            } else {
                $list = $list->toArray();
                foreach ($list as &$user) {
                    $this->processUserInfo($user, $occupation_config, $user_id);
                }
                $result = $list;
            }
        } catch (\Exception $e) {
            $result = [];
        }

        return $result;
    }
    
}
