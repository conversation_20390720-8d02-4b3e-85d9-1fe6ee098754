<?php
declare(strict_types=1);

namespace App\Model;

// use AlibabaCloud\SDK\Dypnsapi\V20170525\Dypnsapi;
// use \Exception;
// use AlibabaCloud\Tea\Exception\TeaError;
// use AlibabaCloud\Tea\Utils\Utils;

// use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dypnsapi\V20170525\Models\VerifyMobileRequest;
// use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;

//use AlibabaCloud\SDK\Dypnsapi\V20170525\Dypnsapi;
use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;
//use AlibabaCloud\Tea\Utils\Utils;

use Darabonba\OpenApi\Models\Config;
//use AlibabaCloud\SDK\Dypnsapi\V20170525\Models\GetMobileRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;


use AlibabaCloud\SDK\Cloudauth\V20190307\Cloudauth;
use AlibabaCloud\SDK\Cloudauth\V20190307\Models\DescribeFaceVerifyRequest;
use AlibabaCloud\SDK\Cloudauth\V20190307\Models\DescribeFaceVerifyResponse;
use AlibabaCloud\SDK\Cloudauth\V20190307\Models\InitFaceVerifyRequest;
use AlibabaCloud\SDK\Cloudauth\V20190307\Models\InitFaceVerifyResponse;
use AlibabaCloud\SDK\Dypnsapi\V20170525\Dypnsapi;
use AlibabaCloud\SDK\Dypnsapi\V20170525\Models\GetMobileRequest;
use AlibabaCloud\Tea\Utils\Utils;

class Sample {
//class Ali extends Model{
    
    /**
     * 使用AK&SK初始化账号Client
     * @param string $accessKeyId
     * @param string $accessKeySecret
     * @return Dypnsapi Client
     */
    public static function createClient($accessKeyId, $accessKeySecret){
        $config = new Config([
            // 必填，您的 AccessKey ID
            "accessKeyId" => $accessKeyId,
            // 必填，您的 AccessKey Secret
            "accessKeySecret" => $accessKeySecret
        ]);
        // 访问的域名
        $config->endpoint = "dypnsapi.aliyuncs.com";
        return new Dypnsapi($config);
    }

    /**
     * @param string[] $args
     * @return void
     */
    public static function main($AccessToken){
        // 工程代码泄露可能会导致AccessKey泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/311677.html
        $client = self::createClient("LTAI5tSAgAZWbcTh1HvXGiXd", "******************************");
        $verifyMobileRequest = new VerifyMobileRequest(['accessToken'=>$AccessToken]);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $client->verifyMobileWithOptions($verifyMobileRequest, $runtime);
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 如有需要，请打印 error
            var_dump($error->message);
            Utils::assertAsString($error->message);
        }
    }
}    