<?php

declare(strict_types=1);

namespace App\Controller;

use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\DbConnection\Db;
use Hyperf\HttpServer\Annotation\Middlewares;
use App\Middleware\Auth\TokenMiddleware;
use Hyperf\Context\Context;
use App\Model\UserMoneyLog;
use App\Model\RechargeGood;
use App\Model\RechargeOrder;
use App\Model\Vip;
use App\Model\User;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

#[AutoController]
#[Middlewares([TokenMiddleware::class])]
class VipController extends AbstractController
{
    #[Inject]
    private Redis $redis;

    private const LANGUAGE_MAP = [
        'zh-hk' => 'hk_name',
        'ko' => 'ko_name',
        'ja' => 'ja_name',
        'en' => 'en_name'
    ];

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix', '');
    }

    /**
     * 获取当前用户ID并验证登录状态
     */
    private function getCurrentUserId(): int
    {
        $user_id = Context::get('user_id', 0);
        if (empty($user_id)) {
            throw new \Exception(\Hyperf\Translation\trans('params.loginError'), 10001);
        }
        return (int)$user_id;
    }

    /**
     * 验证分页参数
     */
    private function validatePage(string $page): int
    {
        return is_num($page) ? (int)$page : 1;
    }

    /**
     * VIP产品列表
     */
    public function good_list(RequestInterface $req)
    {
        try {
            $sys_type = $req->input('sys_type', '1');
            $status = $req->input('status', 1);

            if (!in_array($sys_type, ['1', '2'])) {
                throw new \Exception(\Hyperf\Translation\trans('params.typeError'));
            }

            $headers = $req->getHeaders();
            $lang = $headers['accept-language'][0] ?? 'zh-cn';

            $list = Vip::query()
                ->where('type', $sys_type)
                ->where('switch', 1)
                ->where('status', $status)
                ->orderBy('weigh', 'desc')
                ->select('id as vip_id', 'status', 'name', 'old_price', 'now_price', 'switch', 'type', 'ko_name', 'ja_name', 'hk_name', 'en_name')
                ->when($sys_type == '2', function ($sql) {
                    $sql->addSelect('good_code');
                })
                ->get();

            if ($list) {
                foreach ($list as &$item) {
                    if (isset(self::LANGUAGE_MAP[$lang])) {
                        $name_field = self::LANGUAGE_MAP[$lang];
                        $item['name'] = $item[$name_field];
                    }
                }
            } else {
                $list = [];
            }
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('', $list);
    }


    /**
     * 购买会员，下单
     */
    public function add_vip_order(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $other_id = $req->input('other_id', '');
            if (!$other_id) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            $vip_id = $req->input('vip_id', '');
            if (!is_num($vip_id)) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            $info = Vip::query()->where('id', $vip_id)->first();
            if (!$info) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            $time = time();
            $order_sn = 'VI' . date("Ymdhis") . sprintf("%08d", $user_id) . mt_rand(10000, 999999);
            $data = [
                'order_sn' => $order_sn,
                'user_id' => $other_id,
                'vip_id' => $vip_id,
                'createtime' => $time,
                'status' => '0',
                'price' => $info->now_price,
                'month' => $info->month,
                'unit' => $info->unit,
                'good_code' => $info->good_code,
                'other_id' => $user_id
            ];

            Db::table('vip_order')->insertGetId($data);

            $result = [
                'order_sn' => $order_sn,
                'price' => $info->now_price,
                'good_code' => $info->good_code
            ];
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('', $result);
    }

    public function recharge_info(RequestInterface $req)
    {
        try {
            $this->getCurrentUserId(); // 验证登录状态

            $order_sn = $req->input('order_sn', '');
            $type = substr($order_sn, 0, 2);

            $table_map = [
                'VI' => 'vip_order',
                'RE' => 'recharge_order'
            ];

            if (!isset($table_map[$type])) {
                $result = 0;
                $message = \Hyperf\Translation\trans('params.error');
            } else {
                $info = Db::table($table_map[$type])->where('order_sn', $order_sn)->first();
                if ($info && $info['status'] == 1) {
                    $result = 1;
                    $message = \Hyperf\Translation\trans('params.success');
                } else {
                    $result = 0;
                    $message = \Hyperf\Translation\trans('params.error');
                }
            }
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success($message, $result);
    }


    /**
     * 充值商品列表
     */
    public function recharge_good_list()
    {
        try {
            $list = Db::table('recharge_good')
                ->where('on_switch', 1)
                ->orderBy('weigh', 'desc')
                ->select('id as good_id', 'lable', 'price', 'diamond', 'zengsong_diamond')
                ->get();
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('', $list);
    }


    /**
     * 提交充值
     */
    public function add_recharge(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $good_id = $req->input('good_id', '');
            if (!is_num($good_id)) {
                throw new \Exception(\Hyperf\Translation\trans('params.typeError'));
            }

            $info = RechargeGood::query()->where('id', $good_id)->where('on_switch', 1)->first();
            if (!$info) {
                throw new \Exception(\Hyperf\Translation\trans('params.productNotExist'));
            }

            $info = $info->toArray();
            $order_sn = 'RE' . date("Ymdhis") . sprintf("%08d", $user_id) . mt_rand(10000, 999999);
            $data = [
                'good_id' => $good_id,
                'order_sn' => $order_sn,
                'user_id' => $user_id,
                'amount' => $info['price'],
                'payamount' => 0,
                'diamond' => $info['diamond'] + $info['zengsong_diamond'],
                'createtime' => time(),
                'status' => '0',
                'good_code' => $info['good_code']
            ];

            $result = Db::table('recharge_order')->insert($data);
            if ($result === false) {
                throw new \Exception(\Hyperf\Translation\trans('params.error'));
            }

            $response = [
                'order_sn' => $order_sn,
                'price' => $info['price'],
                'good_code' => $info['good_code']
            ];
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('', $response);
    }


    /**
     * 充值记录
     */
    public function recharge_logs(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $page = $this->validatePage($req->input('page', '1'));
            $limit = 10;

            $list = RechargeOrder::query()
                ->where('user_id', $user_id)
                ->where('status', '1')
                ->select('id as order_id', 'order_sn', 'payamount', 'diamond', 'createtime', 'status', 'paytype')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->orderBy('createtime', 'desc')
                ->get();

            if ($list) {
                foreach ($list as &$item) {
                    $item->createtime_attr = date('Y-m-d H:i:s', $item->createtime);
                }
            } else {
                $list = [];
            }
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('', $list);
    }


    /**
     * 资金记录
     */
    public function money_logs(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $page = $this->validatePage($req->input('page', '1'));
            $limit = 10;

            $list = UserMoneyLog::query()
                ->with("user:id,avatar,nickname,gender")
                ->where('user_id', $user_id)
                ->select('id as log_id', 'user_id', 'money', 'memo', 'createtime')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->orderBy('createtime', 'desc')
                ->get();

            if ($list) {
                foreach ($list as &$item) {
                    $item->createtime_attr = date('Y-m-d H:i:s', $item->createtime);
                }
            } else {
                $list = [];
            }
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('', $list);
    }


    /**
     * 提现申请
     */
    public function withdraw_cash(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $money = $req->input('money', '0');
            $type = $req->input('type', '');

            if (!preg_match('/^[0-9]+(.[0-9]{1,2})?$/', $money)) {
                throw new \Exception(\Hyperf\Translation\trans('params.payMoneyError'));
            }

            if ($money <= 0) {
                throw new \Exception(\Hyperf\Translation\trans('params.PayMoneyError'));
            }

            $config = $this->redis->hmget($this->getRedisPrefix() . 'global_config', ['min_recharge_money', 'max_recharge_times', 'recharge_date', 'recharge_partner_level']);
            $date = date('d');
            if (!in_array($date, explode(',', $config['recharge_date']))) {
                throw new \Exception(\Hyperf\Translation\trans('params.notBetweenDate'));
            }

            $user = User::query()->where('id', $user_id)->select('money', 'wechat', 'alipay', 'wechat_name', 'alipay_name', 'forever_on', 'partner_id', 'is_partner', 'permit_tx')->first();
            if ($user->permit_tx != 1) {
                throw new \Exception(\Hyperf\Translation\trans('params.featureDisabled'));
            }

            if ($user['partner_id'] && !in_array($user->partner_id, explode(',', $config['recharge_partner_level']))) {
                throw new \Exception(\Hyperf\Translation\trans('params.levelError'));
            }

            if (bccomp(strval($money), strval($user->money), 2) == 1) {
                throw new \Exception(\Hyperf\Translation\trans('params.PayMoneyError'));
            }

            $account_map = [
                'alipay' => ['account' => $user->alipay, 'name' => $user->alipay_name, 'error' => 'params.alipayError'],
                'wechat' => ['account' => $user->wechat, 'name' => $user->wechat_name, 'error' => 'params.wechatError']
            ];

            if (!isset($account_map[$type])) {
                throw new \Exception(\Hyperf\Translation\trans('params.typeError'));
            }

            $account_info = $account_map[$type];
            if (!$account_info['account'] || !$account_info['name']) {
                throw new \Exception(\Hyperf\Translation\trans($account_info['error']));
            }

            if ($config['min_recharge_money'] && (bccomp(strval($money), strval($config['min_recharge_money']), 2) == -1)) {
                throw new \Exception(\Hyperf\Translation\trans('params.moneyTooLow'));
            }

            $count = Db::table('withdraw')->where('user_id', $user_id)->whereMonth('createtime', date('m'))->count();
            if ($config['max_recharge_times'] && ($count >= $config['max_recharge_times'])) {
                throw new \Exception(\Hyperf\Translation\trans('params.maxMoneyNum'));
            }

            $data = [
                'orderid' => 'TX' . date("Ymdhis") . sprintf("%08d", $user_id) . mt_rand(10000, 999999),
                'user_id' => $user_id,
                'money' => $money,
                'type' => $type,
                'account' => $account_info['account'],
                'name' => $account_info['name'],
                'createtime' => time()
            ];

            $log = [
                'user_id' => $user_id,
                'money' => -$money,
                'before' => $user->money,
                'after' => $user->money - $money,
                'memo' => \Hyperf\Translation\trans('params.withdrawals'),
                'createtime' => time(),
                'type_name' => '1'
            ];

            Db::transaction(function () use ($user_id, $money, $data, $log) {
                Db::table('user')->where('id', $user_id)->decrement('money', $money);
                Db::table('withdraw')->insert($data);
                Db::table('user_money_log')->insert($log);
            });
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('');
    }


    /**
     * 积分提现申请
     */
    public function withdraw_score_cash(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $money = $req->input('money', '0');
            $type = $req->input('type', '');

            if ($money <= 0) {
                throw new \Exception(\Hyperf\Translation\trans('params.PayMoneyError'));
            }

            $config = $this->redis->hmget($this->getRedisPrefix() . 'global_config', ['min_recharge_money', 'max_recharge_times', 'recharge_date', 'recharge_partner_level']);
            $date = date('d');
            if (!in_array($date, explode(',', $config['recharge_date']))) {
                throw new \Exception(\Hyperf\Translation\trans('params.notBetweenDate'));
            }

            $user = User::query()->where('id', $user_id)->select('from_score', 'wechat', 'alipay', 'wechat_name', 'alipay_name', 'forever_on', 'partner_id', 'is_partner', 'permit_tx')->first();
            if ($user->permit_tx != 1) {
                throw new \Exception(\Hyperf\Translation\trans('params.featureDisabled'));
            }

            if ($user['partner_id'] && !in_array($user->partner_id, explode(',', $config['recharge_partner_level']))) {
                throw new \Exception(\Hyperf\Translation\trans('params.levelError'));
            }

            if (bccomp(strval($money), strval($user->from_score), 2) == 1) {
                throw new \Exception(\Hyperf\Translation\trans('params.PayMoneyError'));
            }

            $account_map = [
                'alipay' => ['account' => $user->alipay, 'name' => $user->alipay_name, 'error' => 'params.alipayError'],
                'wechat' => ['account' => $user->wechat, 'name' => $user->wechat_name, 'error' => 'params.wechatError']
            ];

            if (!isset($account_map[$type])) {
                throw new \Exception(\Hyperf\Translation\trans('params.typeError'));
            }

            $account_info = $account_map[$type];
            if (!$account_info['account'] || !$account_info['name']) {
                throw new \Exception(\Hyperf\Translation\trans($account_info['error']));
            }

            if ($config['min_recharge_money'] && (bccomp(strval($money), strval($config['min_recharge_money']), 2) == -1)) {
                throw new \Exception(\Hyperf\Translation\trans('params.moneyTooLow'));
            }

            $count = Db::table('withdraw')->where('user_id', $user_id)->whereMonth('createtime', date('m'))->count();
            if ($config['max_recharge_times'] && ($count >= $config['max_recharge_times'])) {
                throw new \Exception(\Hyperf\Translation\trans('params.maxMoneyNum'));
            }

            $data = [
                'orderid' => 'TX' . date("Ymdhis") . sprintf("%08d", $user_id) . mt_rand(10000, 999999),
                'user_id' => $user_id,
                'money' => $money,
                'type' => $type,
                'account' => $account_info['account'],
                'name' => $account_info['name'],
                'createtime' => time()
            ];

            $log = [
                'user_id' => $user_id,
                'score' => -$money,
                'before' => $user->from_score,
                'after' => $user->from_score - $money,
                'memo' => \Hyperf\Translation\trans('params.withdrawals'),
                'createtime' => time(),
                'is_ice' => '1',
            ];

            Db::transaction(function () use ($user_id, $money, $data, $log) {
                Db::table('user')->where('id', $user_id)->decrement('from_score', $money);
                Db::table('withdraw')->insert($data);
                Db::table('user_score_log')->insert($log);
            });
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('');
    }

}