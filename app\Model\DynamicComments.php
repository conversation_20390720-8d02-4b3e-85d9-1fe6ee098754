<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\DbConnection\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

/**
 * @property int $id
 * @property int $dynamic_id
 * @property int $user_id
 * @property string $comments
 * @property int $createtime
 * @property int $cmt_num
 */
class DynamicComments extends Model
{
    public bool $timestamps = false;

    protected ?string $table = 'dynamic_comments';
    protected array $fillable = [];
    protected array $casts = [
        'id' => 'integer',
        'dynamic_id' => 'integer',
        'user_id' => 'integer',
        'createtime' => 'integer',
        'cmt_num' => 'integer'
    ];

    #[Inject]
    private Redis $redis;

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix', '');
    }

    /**
     * 获取用户别名
     */
    private function getUserAlias(int $user_id, int $target_user_id): string
    {
        $alias_name = $this->redis->hget($this->getRedisPrefix() . "alias_user_{$user_id}", (string)$target_user_id);
        return $alias_name === false ? '' : $alias_name;
    }

    public function user()
    {
        return $this->belongsTo(User::class, "user_id");
    }

    /**
     * 评论动态
     */
    public static function comment_dynamic($user_id, $dynamic_id, $content)
    {
        try {
            $dynamic = Dynamic::query()->where('id', $dynamic_id)->first();
            if (empty($dynamic)) {
                throw new \Exception(\Hyperf\Translation\trans('params.feedDelete'));
            }

            $comment_data = [
                'dynamic_id' => $dynamic_id,
                'user_id' => $user_id,
                'comments' => $content,
                'createtime' => time()
            ];

            Db::transaction(function () use ($comment_data, $dynamic_id, $dynamic) {
                Db::table('dynamic_comments')->insert($comment_data);
                Db::table('dynamic')->where('id', $dynamic_id)->increment('comments_num', 1);
                Db::table('user_base')->where('user_id', $dynamic->user_id)->increment('comment_num', 1);
            });

            $result = ['msg' => \Hyperf\Translation\trans('params.success'), 'code' => 1];
        } catch (\Exception $e) {
            $result = ['msg' => $e->getMessage(), 'code' => 0];
        }

        return $result;
    }

    /**
     * 点赞/取消点赞动态
     */
    public static function great_dynamic($user_id, $dynamic_id)
    {
        try {
            $dynamic = Dynamic::query()->where('id', $dynamic_id)->select('id', 'user_id', 'great_num')->first();
            if (!$dynamic) {
                throw new \Exception(\Hyperf\Translation\trans('params.success'));
            }

            $existing_like = DynamicGreat::query()->where('user_id', $user_id)->where('dynamic_id', $dynamic_id)->first();

            if ($existing_like) {
                Db::transaction(function () use ($existing_like, $dynamic_id, $dynamic) {
                    Db::table('dynamic_great')->where('id', $existing_like->id)->delete();
                    Db::table('dynamic')->where('id', $dynamic_id)->decrement('great_num', 1);
                    Db::table('user_base')->where('user_id', $dynamic->user_id)->decrement('great_num', 1);
                });
            } else {
                $like_data = [
                    'user_id' => $user_id,
                    'dynamic_id' => $dynamic_id,
                    'createtime' => time(),
                    'to_user_id' => $dynamic->user_id
                ];

                Db::transaction(function () use ($like_data, $dynamic_id, $dynamic) {
                    Db::table('dynamic_great')->insert($like_data);
                    Db::table('dynamic')->where('id', $dynamic_id)->increment('great_num', 1);
                    Db::table('user_base')->where('user_id', $dynamic->user_id)->increment('great_num', 1);
                });
            }

            $result = ['code' => 1, 'msg' => \Hyperf\Translation\trans('params.success')];
        } catch (\Exception $e) {
            $result = ['code' => 0, 'msg' => $e->getMessage()];
        }

        return $result;
    }


    /**
     * 获取评论列表
     */
    public function comments_list($user_id, $dynamic_id, $page, $limit)
    {
        try {
            $list = self::query()
                ->leftjoin('user', 'user.id', '=', 'dynamic_comments.user_id')
                ->where('dynamic_id', $dynamic_id)
                ->select('dynamic_comments.id as comments_id', 'user_id', 'comments', 'dynamic_comments.createtime', 'dynamic_comments.cmt_num', 'avatar', 'nickname', 'is_vip', 'is_auth', 'girl_switch')
                ->orderBy('dynamic_comments.id', 'desc')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->get();

            if (!$list) {
                $result = ['list' => []];
            } else {
                $list = $list->toArray();
                $cmt_blurred_vision = $this->redis->hget($this->getRedisPrefix() . 'global_config', 'cmt_blurred_vision');

                foreach ($list as $index => $comment) {
                    $list[$index]['cmt_blurred_vision'] = $cmt_blurred_vision;
                    $list[$index]['avatar'] = get_avatar($comment['avatar']);
                    $list[$index]['time_date'] = date('Y-m-d H:i', $comment['createtime']);
                    $list[$index]['alias_name'] = $this->getUserAlias($user_id, $comment['user_id']);
                    $list[$index]['nickname'] = $comment['nickname'] ?: '用户已被删除';
                    $list[$index]['is_vip'] = $comment['is_vip'] ?: 0;
                    $list[$index]['is_auth'] = $comment['is_auth'] ?: 0;
                    $list[$index]['girl_switch'] = $comment['girl_switch'] ?: 0;
                }

                $result = ['list' => $list];
            }
        } catch (\Exception $e) {
            $result = ['list' => []];
        }

        return $result;
    }

}
