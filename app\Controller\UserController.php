<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\TokenController as Token;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\DbConnection\Db;
use Hyperf\HttpServer\Annotation\Middlewares;
use App\Middleware\Auth\TokenMiddleware;
use Hyperf\Context\Context;
use App\Model\User;
use App\Model\UserBase;
use App\Model\Photo;
use App\Model\SysNotice;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

#[AutoController]
#[Middlewares([TokenMiddleware::class])]
class UserController extends AbstractController
{
    #[Inject]
    private Redis $redis;

    private const VALID_LANGUAGES = ['zh-cn', 'zh-hk', 'en', 'ja', 'ko', 'de', 'fr'];

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix', '');
    }

    /**
     * 获取当前用户ID并验证登录状态
     */
    private function getCurrentUserId(): int
    {
        $user_id = Context::get('user_id', 0);
        if (empty($user_id)) {
            throw new \Exception(\Hyperf\Translation\trans('params.loginError'), 10001);
        }
        return (int)$user_id;
    }

    /**
     * 验证Token并获取用户信息
     */
    private function validateToken(string $token): array
    {
        $token_user = Token::get($token);
        if ($token_user === false) {
            throw new \Exception(\Hyperf\Translation\trans('params.loginError'), 10001);
        }
        return $token_user;
    }

    /**
     * 处理字段为false的情况
     */
    private function handleFalseFields(array &$data, array $fields): void
    {
        foreach ($fields as $field) {
            if ($data[$field] === false) {
                $data[$field] = '';
            }
        }
    }

    /**
     * 切換語言
     */
    public function updLang(RequestInterface $request)
    {
        try {
            $token = $request->input('token', '');
            $token_user = $this->validateToken($token);

            $lang = $request->input('clang', 'en');
            if (!in_array($lang, self::VALID_LANGUAGES)) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            Db::table('user')->where('id', $token_user['user_id'])->update(['lang' => $lang]);
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('success');
    }

    /**
     * 添加退款记录
     */
    public function createRefundLog(RequestInterface $request)
    {
        try {
            $token = $request->input('token', '');
            $token_user = $this->validateToken($token);

            $country = $request->input('country');
            $id_no = $request->input('id_no');
            $username = $request->input('username');
            $money = $request->input('money');

            if (!$country || !$id_no || !$username || !$money) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            $data = [
                'user_id' => $token_user['user_id'],
                'country' => $country,
                'id_no' => $id_no,
                'username' => $username,
                'money' => $money,
                'createtime' => time()
            ];

            Db::table('refund_log')->insert($data);
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('ok');
    }

    /**
     * 添加聊天记录
     */
    public function add_chat_log(RequestInterface $request)
    {
        try {
            $token = $request->input('token', '');
            $token_user = $this->validateToken($token);

            $user_id = $request->input('user_id', '');
            if (!$user_id) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            $content = $request->input('content', '');
            $data = [
                'from_id' => $token_user['user_id'],
                'to_id' => $user_id,
                'text' => $content,
                'createtime' => time(),
            ];

            Db::table('chat_log')->insert($data);
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('ok');
    }

    public function check_chat_log(RequestInterface $request)
    {
        try {
            $token = $request->input('token', '');
            $token_user = $this->validateToken($token);

            $time = strtotime(date('d'));
            $user_id = $token_user['user_id'];
            $chat = Db::table('chat_log')
                ->where('from_id', $user_id)
                ->where('createtime', '>', $time)
                ->count();

            $result = $chat > 4
                ? error('请开通会员', $chat)
                : success('ok', $chat);
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return $result;
    }


    /**
     * 翻译
     */
    public function translate(RequestInterface $request)
    {
        try {
            $token = $request->input('token', '');
            $fan = $request->input('fan', '');
            $token_user = $this->validateToken($token);

            $user_id = $request->input('user_id', '');
            if (!$user_id) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            $content = $request->input('content', '');

            // 添加聊天记录
            $data = [
                'from_id' => $token_user['user_id'],
                'to_id' => $user_id,
                'text' => $content,
                'createtime' => time(),
            ];
            Db::table('chat_log')->insert($data);

            // 根据翻译次数回显翻译结果
            $trans_num = Db::table('user')
                ->where('id', $user_id)
                ->select(['translation_num', 'vip_type', 'lang'])
                ->first();

            if ($trans_num['translation_num'] <= 0 && $trans_num['vip_type'] != '2') {
                $result = ['info' => ''];
            } else {
                $to_lang = $fan ?: $trans_num['lang'];
                $headers = $request->getHeaders();
                $lang = $headers['accept-language'][0] ?? 'zh-cn';

                if ($lang == $to_lang) {
                    $result = ['info' => ''];
                } else {
                    // 语言代码映射
                    $lang_map = [
                        'zh-cn' => 'zh',
                        'ko' => 'kor',
                        'ja' => 'jp',
                        'zh-hk' => 'cht',
                        'de' => 'de',
                        'fr' => 'fra'
                    ];

                    $to_lang = $lang_map[$to_lang] ?? $to_lang;

                    if (!$to_lang || !$content) {
                        throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
                    }

                    if (!in_array($to_lang, ['zh', 'en', 'cht', 'kor', 'jp', 'fra', 'de'])) {
                        throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
                    }

                    $translation_result = language($content, $to_lang);
                    if ($translation_result && $trans_num['vip_type'] != '2') {
                        Db::table('user')->where('id', $user_id)->decrement('translation_num', 1);
                    }

                    $result = ['info' => $translation_result];
                }
            }
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success(\Hyperf\Translation\trans('params.success'), $result);
    }


    /**
     * 发送消息（弃用）
     */
    public function addChat(RequestInterface $request)
    {
        try {
            $token = $request->input('token', '');
            $token_user = $this->validateToken($token);

            $user_id = $token_user['user_id'];
            $to_id = $request->input('to_id', '');

            if (!is_numeric($to_id) || $to_id < 0) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            $text = $request->input('text', '');
            if (!$text) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            $data = [
                'from_id' => $user_id,
                'to_id' => $to_id,
                'text' => $text,
                'createtime' => time(),
                'read' => 0
            ];

            Db::table('chat_log')->insert($data);
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success(\Hyperf\Translation\trans('params.success'));
    }

    /**
     * 消息状态改为已读
     */
    public function readChat(RequestInterface $request)
    {
        try {
            $token = $request->input('token', '');
            $token_user = $this->validateToken($token);

            $user_id = $token_user['user_id'];
            $to_id = $request->input('to_id', '');

            if (!is_numeric($to_id) || $to_id < 0) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            Db::table('chat_log')
                ->where(['from_id' => $user_id, 'to_id' => $to_id])
                ->update(['read' => 1]);
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success(\Hyperf\Translation\trans('params.success'));
    }


    /**
     * 某人详情
     */
    public function user_info(RequestInterface $req)
    {
        try {
            $token = $req->input('token', '');
            $token_user = $this->validateToken($token);
            $user_id = $token_user['user_id'];

            $info = User::query()->where('id', $user_id)
                ->select('id as user_id', 'score', 'nickname', 'mobile', 'gender', 'birthday', 'bio', 'wx_id', 'height', 'weight', 'characters', 'relation', 'occupation', 'label', 'avatar',
                    'is_vip', 'need_edit', 'girl_switch', 'master_city', 'vip_end_time', 'true_name', 'true_code', 'wechat_name', 'wechat', 'alipay_name',
                    'alipay', 'nearby_hide', 'distance_hide', 'unlock_chat', 'unlock_weixin', 'unlock_chatup', 'is_auth', 'invite_code', 'is_partner',
                    'play_city', 'similar_rate', 'country', 'vip_type', 'username', 'weights', 'time', 'revenue', 'language', 'car_budget', 'meet_budget')
                ->first();

            if (!$info) {
                throw new \Exception(\Hyperf\Translation\trans('params.userEmpty'));
            }

            $info = $info->toArray();
            $info['id'] = $info['user_id'];
            $info['token'] = $token;
            // 获取语言配置
            $headers = $req->getHeaders();
            $lang = $headers['accept-language'][0] ?? 'zh-cn';

            $language_map = [
                'zh-cn' => 'global_select_code',
                'en' => 'global_select_en_code',
                'ja' => 'global_select_ja_code',
                'ko' => 'global_select_ko_code',
                'zh-hk' => 'global_select_hk_code',
                'de' => 'global_select_de_code',
                'fr' => 'global_select_fr_code',
            ];

            $prefix = $this->getRedisPrefix();
            $redis_key = $language_map[$lang] ?? 'global_select_code';
            $select_code = $this->redis->hgetall($prefix . $redis_key);
            $select_color = [];

            // 处理false字段
            $this->handleFalseFields($info, ['characters', 'relation', 'occupation', 'label']);

            // 处理数组字段的函数
            $processArrayField = function($field) use ($info, $select_code, $select_color) {
                if ($info[$field] && $info[$field] !== false) {
                    $items = explode(',', $info[$field]);
                    $result = [];
                    foreach ($items as $item) {
                        $result[] = [
                            'id' => $item,
                            'code' => $select_code[$item] ?? '',
                            'color' => $select_color[$item] ?? ''
                        ];
                    }
                    return $result;
                }
                return [];
            };

            $language_arr = $processArrayField('language');
            $time_arr = $processArrayField('time');
            $car_budget_arr = $processArrayField('car_budget');
            $meet_budget_arr = $processArrayField('meet_budget');

            // 设置数组字段
            $info['meet_budget_arr'] = $meet_budget_arr;
            $info['car_budget_arr'] = $car_budget_arr;
            $info['time_arr'] = $time_arr;
            $info['language_arr'] = $language_arr;
            $info['weights_text'] = $select_code[$info['weights']] ?? '';
            $info['revenue_text'] = $select_code[$info['revenue']] ?? '';
            $info['characters_arr'] = strtr($info['characters'], $select_code);
            $info['relation_arr'] = strtr($info['relation'], $select_code);
            $info['occupation_arr'] = strtr($info['occupation'], $select_code);
            $info['label_arr'] = strtr($info['label'], $select_code);
            $info['avatars'] = half_avatar($info['avatar']);
            $info['avatar'] = get_avatar($info['avatar']);

            // 处理更多false字段
            $this->handleFalseFields($info, ['wechat', 'alipay', 'true_name', 'true_code', 'wechat_name', 'alipay_name']);

            // 处理VIP时间
            $info['vip_end_time_attr'] = ($info['is_vip'] == 'one')
                ? date('Y-m-d H:i', $info['vip_end_time'])
                : '';
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('请求成功', $info);
    }

    public function user_base(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();
            $info = UserBase::query()->where('user_id', $user_id)->first();
        } catch (\Exception $e) {
            return error($e->getMessage(), [], $e->getCode() ?: null);
        }

        return success('', $info);
    }

    /**
     * 修改密码
     */
    public function reset_pwd(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $old_pwd = $req->input('old_pwd', '');
            $new_pwd = $req->input('new_pwd', '');

            if ($old_pwd == $new_pwd) {
                throw new \Exception(\Hyperf\Translation\trans('params.pwdNotSame'));
            }

            if (strlen($new_pwd) < 6) {
                throw new \Exception(\Hyperf\Translation\trans('params.pwdTooShort'));
            }

            $user = User::query()->select('password', 'salt')->find($user_id);
            $old = getEncryptPassword($old_pwd, $user->salt);

            if ($old != $user->password) {
                throw new \Exception(\Hyperf\Translation\trans('params.pwdError'));
            }

            $new = getEncryptPassword($new_pwd, $user->salt);
            $result = Db::table('user')->where('id', $user_id)->update(['password' => $new]);

            if ($result === false) {
                throw new \Exception('');
            }
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('');
    }

    /**
     * 我的认证状态
     * 1图片失败，2图成功，视频未提交 3图成功，视频待审 4图成功，视频失败 5图与视频均成功
     */
    public function auth_status(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $user = Db::table('user')
                ->select('id as user_id', 'is_auth', 'img_auth', 'video_auth', 'auth_image', 'auth_file', 'txy_times', 'video_failure')
                ->where('id', $user_id)
                ->first();

            // 转换为数组并处理false字段
            $user = (array)$user;
            $this->handleFalseFields($user, ['video_failure']);

            $user['auth_image'] = $user['auth_image'] ? get_avatar($user['auth_image']) : '';
            $user['auth_file'] = $user['auth_file'] ? get_avatar($user['auth_file']) : '';

            // 计算认证状态
            $auth_status_map = [
                ['img_auth' => '0', 'video_auth' => null, 'status' => 0],
                ['img_auth' => '3', 'video_auth' => null, 'status' => 1],
                ['img_auth' => '1', 'video_auth' => '0', 'status' => 2],
                ['img_auth' => '1', 'video_auth' => '2', 'status' => 3],
                ['img_auth' => '1', 'video_auth' => '3', 'status' => 4],
                ['img_auth' => '1', 'video_auth' => '1', 'status' => 5],
            ];

            foreach ($auth_status_map as $map) {
                if ($user['img_auth'] == $map['img_auth'] &&
                    ($map['video_auth'] === null || $user['video_auth'] == $map['video_auth'])) {
                    $user['auth_status'] = $map['status'];
                    break;
                }
            }
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('', $user);
    }

    public function photo_self(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $page = $req->input('page', '1');
            if (!is_num($page)) {
                $page = 1;
            }

            $limit = 10;
            $photos = Photo::query()
                ->where('user_id', $user_id)
                ->orderBy('id', 'desc')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->get();

            if (!$photos) {
                $result = [];
            } else {
                $result = $photos->toArray();
                foreach ($result as &$photo) {
                    $photo['image'] = get_avatar($photo['image']);
                }
            }
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('', $result);
    }


    //相册 【1普展示  2待付 3已付   4待焚  5已焚】
    public function photo_list(RequestInterface $req)
    {
        $user_id = Context::get('user_id', 0);
        if ($user_id == false) {
            return error(\Hyperf\Translation\trans('params.loginError'), null, '10001');
        }

        $type = $req->input('type', '');
        $uid  = $req->input('uid', '');
        if (!in_array($type, [1, 2, 3, 4])) {
            return error(\Hyperf\Translation\trans('params.typeError'));
        }
        if (!is_num($uid)) {
            return error(\Hyperf\Translation\trans('params.codeError'));
        }
        $li = Photo::query()->where('user_id', $uid)
            ->where('status', 2)
            ->when($type == '4', function ($sql) {
                $sql->where('type_status', '1')->where('file_type', '2');
            }, function ($sql) use ($type) {
                $sql->where('type_status', $type)->where('file_type', '1');
            })
            ->get();
        if (!$li) {
            return success('', []);
        } else {
            $li = $li->toArray();
        }
        switch ($type) {
            case 1:
            case 4:
                foreach ($li as $k => $v) {
                    $li[$k]['image']       = get_avatar($v['image']);
                    $li[$k]['look_status'] = 1;
                }
                break;
            case 2:
                if ($user_id == $uid) {//自己不需要付费
                    foreach ($li as $k => $v) {
                        $li[$k]['image']       = get_avatar($v['image']);
                        $li[$k]['look_status'] = 1;
                    }
                } else {
                    $ids = Db::table('photo_log')->where('user_id', $user_id)->whereIn('photo_id', array_column($li, 'id'))->pluck('photo_id');
                    if ($ids) {
                        $ids = $ids->toArray();
                    } else {
                        $ids = [];
                    }
                    foreach ($li as $x => $y) {
                        $li[$x]['image'] = get_avatar($y['image']);
                        if (in_array($y['id'], $ids)) {
                            $li[$x]['look_status'] = 3;
                        } else {
                            $li[$x]['look_status'] = 2;
                        }
                    }
                }
                break;
            case 3:
                if ($user_id == $uid) {//自己不需要隐藏
                    foreach ($li as $k => $v) {
                        $li[$k]['image_attr']  = get_avatar($v['image']);
                        $li[$k]['look_status'] = 1;
                    }
                } else {
                    $ids = Db::table('photo_log')->where('user_id', $user_id)->whereIn('photo_id', array_column($li, 'id'))->pluck('photo_id');
                    if ($ids) {
                        $ids = $ids->toArray();
                    } else {
                        $ids = [];
                    }
                    $is_vip = Db::table('user')->where('id', $user_id)->value('is_vip');
                    foreach ($li as $x => $y) {
                        $li[$x]['image'] = get_avatar($y['image']);
                        if (in_array($y['id'], $ids)) {
                            $li[$x]['look_status'] = 5;
                        } else {
                            $li[$x]['look_status'] = 4;
                            if ($is_vip == 'three') {
                                $li[$x]['look_leng'] = 5;
                            } elseif ($is_vip == 'one') {
                                $li[$x]['look_leng'] = 9;
                            }
                        }
                    }
                }
                break;
        }
        return success('', $li);
    }


    //Ta的相册 【1普展示  2待付 3已付   4待焚  5已焚】
    public function photo_group(RequestInterface $req)
    {
        $user_id = Context::get('user_id', 0);
        if ($user_id == false) {
            return error(\Hyperf\Translation\trans('params.loginError'), null, '10001');
        }
        $uid = $req->input('uid', '');
        if (!is_num($uid)) {
            return error(\Hyperf\Translation\trans('params.codeError'));
        }

        $li1 = Photo::where('user_id', $uid)->where('status', '2')->where('type_status', '1')->where('file_type', '1')->orderBy('id', 'desc')->limit(3)->get();
        $li2 = Photo::where('user_id', $uid)->where('status', '2')->where('type_status', '2')->where('file_type', '1')->orderBy('id', 'desc')->limit(3)->get();
        $li3 = Photo::where('user_id', $uid)->where('status', '2')->where('type_status', '3')->where('file_type', '1')->orderBy('id', 'desc')->limit(3)->get();
        $li4 = Photo::where('user_id', $uid)->where('status', '2')->where('type_status', '1')->where('file_type', '2')->orderBy('id', 'desc')->limit(3)->get();
        if (!$li1 && !$li2 && !$li3 && !$li4) {
            return success(\Hyperf\Translation\trans('params.success'), []);
        } else {
            if ($li1) {
                $li1 = $li1->toArray();
            } else {
                $li1 = [];
            }
            if ($li2) {
                $li2 = $li2->toArray();
            } else {
                $li2 = [];
            }
            if ($li3) {
                $li3 = $li3->toArray();
            } else {
                $li3 = [];
            }
            if ($li4) {
                $li4 = $li4->toArray();
            } else {
                $li4 = [];
            }
            $li = array_merge($li1, $li2, $li3, $li4);
        }
        $ids = [];
        foreach ($li as $x => $y) {
            $li[$x]['image'] = get_avatar($y['image']);
            $ids[]           = $y['id'];
        }
        $prefix = $this->getRedisPrefix();
        $config = $this->redis->hmget($prefix . 'global_config', ['fire_leng', 'vip_fire_leng']);

        $ifs = Db::table('photo_log')->where('user_id', $user_id)->whereIn('photo_id', $ids)->pluck('photo_id');
        if (!$ifs) {
            $ifs = [];
        } else {
            $ifs = $ifs->toArray();
        }
        $list   = ['video' => [], 'image_pub' => [], 'image_pay' => [], 'image_fire' => []];
        $is_vip = Db::table('user')->where('id', $user_id)->value('is_vip');
        foreach ($li as $x => &$y) {
            if ($y['file_type'] == '2') {
                $y['look_status'] = 1;
                $list['video'][]  = $y;
            } elseif ($y['type_status'] == '1') {
                $y['look_status']    = 1;
                $list['image_pub'][] = $y;
            } elseif ($y['type_status'] == '2') {
                if (in_array($y['id'], $ifs)) {
                    $y['look_status'] = 3;
                } else {
                    $y['look_status'] = 2;
                }
                $list['image_pay'][] = $y;
            } elseif ($y['type_status'] == '3') {
                if (in_array($y['id'], $ifs)) {
                    $y['look_status'] = 5;
                    $y['look_leng']   = 0;
                } else {
                    $y['look_status'] = 4;
                    if ($is_vip == 'three') {
                        $y['look_leng'] = $config['fire_leng'];
                    } elseif ($is_vip == 'one') {
                        $y['look_leng'] = $config['vip_fire_leng'];
                    }
                }
                $list['image_fire'][] = $y;
            }
        }

        return success('', $list);
    }

    //上传相册
    public function up_photo(RequestInterface $req)
    {
        $user_id = Context::get('user_id', 0);
        if ($user_id == false) {
            return error(\Hyperf\Translation\trans('params.loginError'), null, '10001');
        }
        $file_type = $req->input('file_type', '1');
        $type      = $req->input('type', '1');
        $image     = $req->input('image', '');
        $coin      = $req->input('coin', 0);
        if ($image == '') {
            return error(\Hyperf\Translation\trans('params.fileNotExist'));
        }
        if (!in_array($type, [1, 2, 3])) {
            return error(\Hyperf\Translation\trans('params.chooseType'));
        }
        if (!in_array($file_type, [1, 2])) {
            return error(\Hyperf\Translation\trans('params.typeError'));
        }
        /*if ($type == '2' && !is_num($coin)) {
            return error(\Hyperf\Translation\trans('params.payMoneyError'));
        }*/
        $arr = [
            'file_type'   => $file_type,
            'user_id'     => $user_id,
            'type_status' => $type,
            'image'       => $image,
            'coin'        => $coin,
        ];
        $li  = Photo::insert($arr);
        if ($li === false) {
            return error(\Hyperf\Translation\trans('params.error'));
        } else {
            return success(\Hyperf\Translation\trans('params.success'));
        }
    }

    //删除相册
    public function del_photo(RequestInterface $req)
    {
        $user_id = Context::get('user_id', 0);
        if ($user_id == false) {
            return error(\Hyperf\Translation\trans('params.loginError'), null, '10001');
        }
        $photo_ids = $req->input('photo_ids', '');
        if (preg_match('/[^,\d]/', $photo_ids)) {
            return error(\Hyperf\Translation\trans('params.codeError'));
        }
        $li = explode(',', $photo_ids);
        $r  = Photo::whereIn('id', $li)->where('user_id', $user_id)->delete();
        if ($r === false) {
            return error(\Hyperf\Translation\trans('params.error'));
        } else {
            Db::table('photo_log')->whereIn('photo_id', $li)->delete();
            return success(\Hyperf\Translation\trans('params.success'));
        }
    }

    //付费相册
    public function pay_photo(RequestInterface $req)
    {
        $user_id = Context::get('user_id', 0);
        if ($user_id == false) {
            return error(\Hyperf\Translation\trans('params.loginError'), null, '10001');
        }
        $photo_id = $req->input('photo_id', '');
        if (!is_num($photo_id)) {
            return error(\Hyperf\Translation\trans('params.codeError'));
        }
        $info = Db::table('photo')->where('id', $photo_id)->first();
        if (!$info) {
            return error(\Hyperf\Translation\trans('params.fileNotExist'));
        }
        if ($info['user_id'] == $user_id || $info['type_status'] != '2') {
            return error(\Hyperf\Translation\trans('params.noPay'));
        }
        $if = Db::table('photo_log')->where('photo_id', $photo_id)->where('user_id', $user_id)->count();
        if ($if) {
            return error(\Hyperf\Translation\trans('params.paid'));
        }
        $user_info = User::query()->select('score', 'is_vip')->find($user_id);
        if ($user_info->score < $info['coin']) {
            return error(\Hyperf\Translation\trans('params.diamondsNotEnough'));
        }
        //减钻石
        Db::table('user')->where('id', $user_id)->decrement('score', $info['coin']);
        //钻石日志
        $add = [
            'user_id'    => $user_id,
            'score'      => -($info['coin']),
            'before'     => $user_info->score,
            'after'      => $user_info->score - $info['coin'],
            'memo'       => \Hyperf\Translation\trans('params.payPhoto'),
            'createtime' => time(),
        ];
        Db::table('user_score_log')->insert($add);
        //相册日志
        Db::table('photo_log')->insert(['photo_id' => $photo_id, 'user_id' => $user_id]);
        //返佣
        $add = ['action' => 'parent_get_money', 'user_id' => $user_id, 'num' => $info['coin'], 'type' => 'look_photo'];
        // $this->nsq->publish('money', json_encode($add));
        return success(\Hyperf\Translation\trans('params.success'));
    }

    //记录阅后即焚
    public function add_self_die(RequestInterface $req)
    {
        $user_id = Context::get('user_id', 0);
        if ($user_id == false) {
            return error(\Hyperf\Translation\trans('params.loginError'), null, '10001');
        }
        $photo_id = $req->input('photo_id', '');
        if (!is_num($photo_id)) {
            return error(\Hyperf\Translation\trans('params.codeError'));
        }
        $r = Db::table('photo_log')->insert(['user_id' => $user_id, 'photo_id' => $photo_id]);
        if ($r === false) {
            return error(\Hyperf\Translation\trans('params.error'));
        } else {
            return success();
        }
    }


    //三个随机人,,,非关注，非拉黑关系，正常且异性
    public function random_user(RequestInterface $req)
    {
        $user_id = Context::get('user_id', 0);
        if ($user_id == false) {
            return error(\Hyperf\Translation\trans('params.loginError'), null, '10001');
        }
        $header       = $req->getHeaders();
        $lang         = $header['accept-language'][0] ?? 'zh-cn';
        $gender       = Db::table('user')->where('id', $user_id)->value('gender');
        $other_gender = $gender == 2 ? 1 : 2;
        $where        = [];
        if ($other_gender == '2') {
            $where[] = ['gender', '=', 2];
            $where[] = ['girl_switch', '=', 0];
        } elseif ($other_gender == '1') {
            $where[] = ['gender', '=', 1];
            $where[] = ['is_vip', '=', 'three'];
        }
        $prefix = $this->getRedisPrefix();
        $occupation = $this->redis->hgetall($prefix . 'global_select_code');
        $ids = $this->redis->smembers("{$prefix}chat_up_{$user_id}");
        $kefu = $this->redis->hMGet($prefix . 'global_config', ['set_customer_service']);

        $list = User::query()
            ->where($where)
            ->where('have_exit', 0)
            ->where('status', 'normal')
            ->where('is_ls', 1)
            ->where('girl_switch', 0)
            ->whereNotIn('id', function ($sql) use ($user_id) {
                $sql->from('follow')->where('from_id', $user_id)->select('to_id as user_id');
            })
            ->whereNotIn('id', function ($sql) use ($user_id) {
                $sql->from('forbid_user')->where('from_id', $user_id)->select('to_id as user_id');
            })
            ->whereNotIn('id', function ($sql) use ($user_id) {
                $sql->from('forbid_user')->where('to_id', $user_id)->select('from_id as user_id');
            })
            ->when(!empty($ids), function ($sql) use ($ids) {
                $sql->whereNotIn('id', $ids);
            })
            ->when(!empty($kefu['set_customer_service']), function ($sql) use ($kefu) {
                $sql->whereNotIn('id', [$kefu['set_customer_service']]);
            })
            ->select('id as user_id', 'country', 'vip_type', 'forever_on', 'is_ls', 'nickname', 'avatar', 'birthday', 'height', 'weight', 'is_vip', 'is_auth', 'occupation', 'girl_switch', 'gender')
            ->inRandomOrder()
            ->limit(3)
            ->get();
        if (!$list) {
            return success('', []);
        } else {
            $list = $list->toArray();
            foreach ($list as &$y) {
                $str = "";
                if (in_array($lang, ['en', 'ja', 'ko','de','fr'])) {
                    $str = "_{$lang}";
                }
                if ($lang == 'zh-hk') {
                    $str = "_hk";
                }
                if ($other_gender == 1) {
                    $y['greet_word'] = $this->redis->srandmember($prefix . 'greeting_x' . $str);
                } else {
                    $y['greet_word'] = $this->redis->srandmember($prefix . 'greeting_y' . $str);
                }
                $y['avatar'] = get_avatar($y['avatar']);
                if ($y['occupation'] == false) {
                    $y['occupation']     = '';
                    $y['occupation_arr'] = '';
                } else {
                    $y['occupation_arr'] = strtr($y['occupation'], $occupation);
                }
                if ($y['birthday'] == false) {
                    $y['age']      = 0;
                    $y['birthday'] = '';
                } else {
                    $y['age'] = get_age($y['birthday']);
                }
                if ($y['height'] == false) {
                    $y['height'] = '';
                }
            }
            return success('', $list);
        }
    }

    //记录搭讪人
    public function write_random(RequestInterface $req)
    {
        $user_id = Context::get('user_id', 0);
        if ($user_id == false) {
            return error(\Hyperf\Translation\trans('params.loginError'), null, '10001');
        }
        $user_ids = $req->input('user_ids', '');
        if ($user_ids == '') {
            return error(\Hyperf\Translation\trans('params.codeError'),null);
        }
        $user = User::query()->where('id', $user_id)->select(['unlock_chatup', 'vip_type'])->first();
        if ($user->vip_type < 1) {
            return error(\Hyperf\Translation\trans('params.vipError'),null);
        }
        if ($user->unlock_chatup <= 0) {
            return success('', ['unlock_chatup' => $user->unlock_chatup]);
        }
        $prefix = $this->getRedisPrefix();
        $this->redis->sadd($prefix . 'chat_up_all', "{$user_id}");
        $ids = explode(',', $user_ids);
        foreach ($ids as $y) {
            $this->redis->sadd("{$prefix}chat_up_{$user_id}", "{$y}");
        }
        Db::table('user')->where('id', $user_id)->decrement('unlock_chatup', 1);
        return success(\Hyperf\Translation\trans('params.success'), ['unlock_chatup' => $user->unlock_chatup - 1]);
    }

    /**
     * 人气前五名
     */
    public function top_user()
    {
        try {
            $user_id = $this->getCurrentUserId();

            $gender = Db::table('user')->where('id', $user_id)->value('gender');
            $gender = $gender == 0 ? 2 : ($gender == 1 ? 2 : 1);

            $list = User::query()
                ->where('status', 'normal')
                ->where('girl_switch', 0)
                ->where('is_ls', 1)
                ->where('have_exit', 0)
                ->where('gender', $gender)
                ->select('id as user_id', 'nickname', 'is_ls', 'avatar', 'is_vip', 'is_auth', 'girl_switch', 'gender', 'forever_on')
                ->orderBy('son_money', 'desc')
                ->limit(5)
                ->get();

            if (!$list) {
                $result = [];
            } else {
                $result = $list->toArray();
                foreach ($result as $index => &$user) {
                    $user['sort'] = $index + 1;
                    $user['avatar'] = get_avatar($user['avatar']);
                }
            }
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('', $result);
    }

    /**
     * 举报某人
     */
    public function report_user(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $content = $req->input('content', '');
            $type = $req->input('type', '');
            $images = $req->input('images', '');
            $email = $req->input('email', '');
            $nickname = $req->input('nickname', '');

            $len = mb_strlen($content, 'utf8');
            if ($len < 5) {
                throw new \Exception(\Hyperf\Translation\trans('params.wordTooShort'));
            }
            if ($len > 300) {
                throw new \Exception(\Hyperf\Translation\trans('params.wordTooLong'));
            }

            $data = [
                'type' => $type,
                'user_id' => $user_id,
                'beuser_id' => 0,
                'content' => $content,
                'images' => $images,
                'createtime' => time(),
                'nickname' => $nickname,
                'email' => $email
            ];

            $result = Db::table('report')->insert($data);
            if ($result === false) {
                throw new \Exception('');
            }
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success();
    }


    public function sys_notice_num()
    {
        try {
            $user_id = $this->getCurrentUserId();

            $count = Db::table('sys_notice')
                ->leftJoin('sys_notice_log', 'sys_notice.id', '=', 'sys_notice_log.sys_notice_id')
                ->whereIn('sys_notice.user_id', [$user_id, 0])
                ->whereNull('sys_notice_log.id')
                ->count();
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success('ok', compact('count'));
    }


    //系统消失(查完关闭未读数)
    public function sys_list(RequestInterface $req)
    {
        $user_id = Context::get('user_id', 0);
        if ($user_id == false) {
            return error(\Hyperf\Translation\trans('params.loginError'), null, '10001');
        }
//        $time  = Db::table('user')->where('id', $user_id)->value('createtime');
        $page  = $req->input('page', '');
        $limit = $req->input('limit', '10');
        if (!is_num($page)) {
            $page = 1;
        }
        if (!is_num($limit)) {
            $limit = 10;
        }
//        $createtime = Db::table('user')->where('id', $user_id)->value('createtime');
        $list = SysNotice::query()
            ->whereIn('user_id', [$user_id, 0])
            /*->where('createtime', '>=', $time)*/
            ->offset(($page - 1) * $limit)
            ->limit(intval($limit))
            ->orderBy('id', 'desc')
            ->get();
        if (!$list) {
            return success('', []);
        } else {
            $list = $list->toArray();
        }
        foreach ($list as &$y) {
            $y['createtime_attr'] = date('Y-m-d H:i', $y['createtime']);
            if ($y['user_id'] == 0 || $y['user_id'] == $user_id) {
                $is_have = Db::table('sys_notice_log')->where('user_id', $user_id)->where('sys_notice_id', $y['id'])->value('id');
                if (!$is_have) {
                    Db::table('sys_notice_log')->insert([
                        'user_id'       => $user_id,
                        'sys_notice_id' => $y['id'],
                        'createtime'    => time()
                    ]);
                }
            }
        }
//        Db::table('sys_notice')->where('user_id',$user_id)->where('msg_status','0')->update(['msg_status'=>'1']);
        // Db::name('user')->where('id',$user_id)->update(['is_read'=>0]);
        return success('', $list);
    }

    /**
     * 我的邀请列表
     */
    public function invite_list(RequestInterface $request)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $page = $request->post("page", 1);
            $limit = $request->post("limit", 20);

            $list = User::query()
                ->where("p_user", $user_id)
                ->selectRaw("id,avatar,nickname,gender,son_money,partner_id,partner_status,is_partner")
                ->offset(($page - 1) * $limit)
                ->limit(intval($limit))
                ->orderBy('id', 'desc')
                ->get();
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success(\Hyperf\Translation\trans('params.success'), $list);
    }

    /**
     * 提现记录
     */
    public function cash_log(RequestInterface $request)
    {
        try {
            $user_id = $this->getCurrentUserId();

            $page = $request->post("page", 1);
            $limit = $request->post("limit", 20);

            $list = Db::table('withdraw')
                ->where("user_id", $user_id)
                ->offset(($page - 1) * $limit)
                ->limit(intval($limit))
                ->orderBy('id', 'desc')
                ->get();
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success(\Hyperf\Translation\trans('params.success'), $list);
    }

    /**
     * 邀请奖励规则
     */
    public function invite_award_rule()
    {
        try {
            $this->getCurrentUserId(); // 验证登录状态

            $prefix = $this->getRedisPrefix();
            $arr = $this->redis->hMGet($prefix . 'global_config', [
                "register_send_money",
                "register_send_diamond",
                "invite_success_award",
                "invited_success_award"
            ]);
        } catch (\Exception $e) {
            return error($e->getMessage(), null, $e->getCode() ?: null);
        }

        return success(\Hyperf\Translation\trans('params.success'), $arr);
    }
}