<?php

declare(strict_types=1);

namespace App\Nsq\Consumer;

use Hyperf\Nsq\AbstractConsumer;
use Hyperf\Nsq\Annotation\Consumer;
use Hyperf\Nsq\Message;
use Hyperf\Nsq\Result;
use Hyperf\Redis\RedisFactory;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;
use GuzzleHttp\Client;
use Hyperf\Guzzle\CoroutineHandler;
use GuzzleHttp\HandlerStack;
use Hyperf\Nsq\Nsq;
use RongCloud\RongCloud;

#[Consumer(topic: 'user', channel: 'tangx', name: 'UserConsumer', nums: 1)]
class UserConsumer extends AbstractConsumer
{
    private $redis;
    private $prefix;
    private $client;
    private $rongCloud;

    public function __construct()
    {
        $container = ApplicationContext::getContainer();
        $this->redis = $container->get(RedisFactory::class)->get('default');
        $this->prefix = env('redis_prefix');

        $this->client = new Client([
            'handler' => HandlerStack::create(new CoroutineHandler()),
            'timeout' => 5,
            'swoole' => [
                'timeout' => 5,
                'socket_buffer_size' => 1024 * 1024 * 2,
            ],
        ]);

        $this->rongCloud = new RongCloud('bmdehs6pbnifs', 'bG3SOOFNUOhQ6');
    }

    public function isEnable(): bool
    {
        return false;
    }

    public function consume(Message $payload): ?string
    {
        $arr = json_decode($payload->getBody(), true);

        $actions = [
            'city_lnglat' => fn() => $this->city_lnglat($arr),
            'head_upload' => fn() => $this->head_upload($arr),
            'photo_upload' => fn() => $this->photo_upload($arr),
            'imgs_upload' => fn() => $this->imgs_upload($arr),
            'hellow_project' => fn() => $this->hellow_project($arr),
            'hellow_user' => fn() => $this->hellow_user($arr),
            'visit_user' => fn() => $this->visit_user($arr)
        ];

        if (isset($actions[$arr['action']])) {
            $actions[$arr['action']]();
        }

        return Result::ACK;
    }

    public function city_lnglat($arr)
    {
        $name = $arr['point_city'];
        $user_id = $arr['user_id'];

        $response = $this->client->get("https://restapi.amap.com/v3/geocode/geo?address={$name}&key=e16a5f8e63362910e0005c54a50eba12");

        if ($response->getStatusCode() !== 200) {
            return;
        }

        $data = json_decode($response->getBody()->getContents(), true);

        if (!isset($data['geocodes'][0]['location'])) {
            return;
        }

        $location_parts = explode(',', $data['geocodes'][0]['location']);
        $lng_lat = rand_lnglat($location_parts[0], $location_parts[1]);

        if ($lng_lat['lng'] && $lng_lat['lat']) {
            Db::table('user')->where('id', $user_id)->update([
                'master_city' => $name,
                'lng' => $lng_lat['lng'],
                'lat' => $lng_lat['lat'],
                'point_province' => $data['geocodes'][0]['province']
            ]);
        }
    }

    public function head_upload($arr)
    {
        $user_id = $arr['user_id'];
        $head = $arr['head'];

        if (strpos($head, '.') === 0) {
            $head = substr($head, 1);
        }

        $this->client->get("https://hwsj-web.jiangkukeji.cn/api/index/upload_head?user_id={$user_id}&head={$head}");
    }
    public function photo_upload($arr)
    {
        $user_id = $arr['user_id'];
        $imgs = explode(',', $arr['imgs']);

        $img_arr = array_map(function ($img) {
            return strpos($img, '.') === 0 ? substr($img, 1) : $img;
        }, $imgs);

        $this->client->get(env('uri') . 'api/index/upload_photo?user_id=' . $user_id . '&imgs=' . implode(',', $img_arr));
    }

    public function imgs_upload($arr)
    {
        $imgs = $arr['imgs'];
        $this->client->get(env('uri') . 'api/index/imgs_upload?imgs=' . $imgs);
    }
    public function hellow_project($arr)
    {
        $times = mt_rand(3, 5);
        $user_ids = Db::table('user')
            ->where('point_city', $arr['city'])
            ->where('forever_on', '1')
            ->inRandomOrder()
            ->pluck('id');

        $key = $arr['gender'] == '2' ? 'greeting_x' : 'greeting_y';

        $start = time();
        $end = $start + 3600;
        $unix_arr = rand_time_point($start, $end, $times);
        sort($unix_arr);

        $container = ApplicationContext::getContainer();
        $nsq = $container->get(Nsq::class);

        for ($i = 0; $i < $times; $i++) {
            if (!isset($user_ids[$i])) {
                continue;
            }

            $con = $this->redis->srandmember($this->prefix . $key);
            $add = [
                'action' => 'hellow_user',
                'from_id' => $user_ids[$i],
                'to_id' => $arr['user_id'],
                'con' => $con
            ];
            $diff_time = $unix_arr[$i] - $start;
            $nsq->publish('user', json_encode($add), $diff_time);
        }
    }
    public function hellow_user($arr)
    {
        $this->send_msg($arr['from_id'], $arr['to_id'], $arr['con']);
    }

    private function send_msg($from_id, $to_id, $data)
    {
        $message = [
            'senderId' => $from_id,
            'targetId' => $to_id,
            'objectName' => 'RC:TxtMsg',
            'content' => json_encode(['content' => $data], JSON_UNESCAPED_UNICODE)
        ];

        $this->rongCloud->getMessage()->Person()->send($message);
    }

    public function visit_user($arr)
    {
        $user_id = $arr['from_id'];
        $uid = $arr['to_id'];
        $current_time = time();

        $existing_visit = Db::table('visit')
            ->where('from_id', $user_id)
            ->where('to_id', $uid)
            ->first();

        if ($existing_visit) {
            Db::table('visit')->where('id', $existing_visit['id'])->update([
                'createtime' => $current_time,
                'total_num' => $existing_visit['total_num'] + 1
            ]);
        } else {
            Db::table('visit')->insert([
                'from_id' => $user_id,
                'to_id' => $uid,
                'createtime' => $current_time,
                'total_num' => 1
            ]);
        }

        Db::table('user_base')->where('user_id', $uid)->increment('visit_num', 1);
    }
}
