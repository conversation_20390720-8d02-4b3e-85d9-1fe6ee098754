<?php

declare(strict_types=1);

namespace App\Nsq\Consumer;

use Hyperf\Nsq\AbstractConsumer;
use Hyperf\Nsq\Annotation\Consumer;
use Hyperf\Nsq\Message;
use Hyperf\Nsq\Result;
use Hyperf\Redis\RedisFactory;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;

#[Consumer(topic: 'token', channel: 'tangx', name: 'TokenConsumer', nums: 1)]
class TokenConsumer extends AbstractConsumer
{
    private $redis;
    private $prefix;

    public function __construct()
    {
        $container = ApplicationContext::getContainer();
        $this->redis = $container->get(RedisFactory::class)->get('default');
        $this->prefix = env('redis_prefix');
    }

    public function isEnable(): bool
    {
        return true;
    }

    public function consume(Message $payload): ?string
    {
        $arr = json_decode($payload->getBody(), true);

        if ($arr['action'] === 'on_line') {
            $this->on_line($arr['user_id']);
        }

        return Result::ACK;
    }

    public function on_line($user_id)
    {
        $current_time = time();
        $user_id_str = strval($user_id);

        $this->redis->hset($this->prefix . 'global_online', $user_id_str, strval($current_time));
        $this->redis->setex($this->prefix . 'on_line_' . $user_id, 180, 1);

        if (!$this->redis->get($this->prefix . 'online_time_' . $user_id)) {
            Db::table('user')->where('id', $user_id)->update(['online_time' => $current_time]);
            $this->redis->setex($this->prefix . 'online_time_' . $user_id, 180, '1');
        } 
    }
}
