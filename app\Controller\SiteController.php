<?php

declare(strict_types=1);

namespace App\Controller;

use Hyperf\Config\Annotation\Value;
use Hyperf\Contract\ConfigInterface;
use Hyperf\Di\Annotation\Inject;

use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\DbConnection\Db;

use App\Service\QueueService;

#[AutoController]
class SiteController
{
    public function index(RequestInterface $request, ResponseInterface $response){
        $name = $request->input('name');
        $users = Db::table('user')->where('nickname','like','%'.$name.'%')->select('username', 'mobile')->get();
        return $response->json($users);
    }
    
    #[Value("databases.default.pool.connect_timeout")]
    
    private $config;
    public function index_page(RequestInterface $request, ResponseInterface $response){
        $name = $request->input('name');
        $app_name = $this->config;
        $users['app_name'] = $app_name;
        return $response->json($users);
    }
    #[Inject]
    /**
     * @var ConfigInterface
     */
    public $name;
    public function index_page2(RequestInterface $request, ResponseInterface $response){
        $name = $request->input('name');
        $app_name = $this->name->get('app_name');;
        //var_dump($app_name);
        $users['app_name'] = $app_name;
        return $response->json($users);
    }
    
    /**
     * @var QueueService
     */
    #[Inject]
    protected $service;

    /**
     * 注解模式投递消息
     */
    public function test_queue(){
        $this->service->example([
            '<EMAIL>',
            'https://doc.hyperf.io',
            'https://www.hyperf.io',
        ]);

        return 'success';
    }
    
    
}