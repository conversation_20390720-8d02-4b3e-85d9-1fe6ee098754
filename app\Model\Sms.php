<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\DbConnection\Db;
use Hyperf\DbConnection\Model\Model;
use App\Model\Ucpas;

/**
 * @property int $id
 * @property string $event
 * @property string $mobile
 * @property string $code
 * @property int $times
 * @property string $ip
 * @property int $createtime
 */
class Sms extends Model
{
    public bool $timestamps = false;

    protected ?string $table = 'sms';
    protected array $fillable = ['event'];
    protected array $casts = [
        'id' => 'integer',
        'times' => 'integer',
        'createtime' => 'integer'
    ];

    private const EXPIRE_TIME = 120;
    private const MAX_CHECK_NUMS = 10;

    /**
     * 获取最后一次手机发送的数据
     */
    public static function get($mobile, $event = 'default')
    {
        try {
            $sms = self::where('mobile', $mobile)
                ->where('event', $event)
                ->orderBy('id', 'DESC')
                ->first();

            $result = $sms ?: null;
        } catch (\Exception $e) {
            $result = null;
        }

        return $result;
    }

    /**
     * 发送验证码
     *
     * @param   int    $mobile 手机号
     * @param   int    $code   验证码,为空时将自动生成4位数字
     * @param   string $event  事件
     * @return  boolean
     */
    public static function send($mobile, $code = null, $event = 'default', $ip = '')
    {
        try {
            $code = $code ?? mt_rand(1000, 9999);
            $time = time();

            $sms_id = self::insertGetId([
                'event' => $event,
                'mobile' => $mobile,
                'code' => $code,
                'ip' => $ip,
                'createtime' => $time
            ]);
            $options = [
                'clientid' => 'b1ltg0',
                'password' => md5('12345678')
            ];

            $ucpass = new Ucpas($options);
            $sms_result = $ucpass->SendSms('2854', $code, $mobile, '');
            $sms_result = json_decode($sms_result, true);

            if ($sms_result['msg'] !== 'OK') {
                self::where('id', $sms_id)->delete();
                $result = error($sms_result['msg']);
            } else {
                $result = success('发送成功');
            }
        } catch (\Exception $e) {
            $result = error('发送失败');
        }

        return $result;
    }

    /**
     * 发送通知
     */
    public static function notice($mobile, $msg = '', $template = null)
    {
        try {
            $result = false;
        } catch (\Exception $e) {
            $result = false;
        }

        return $result;
    }

    /**
     * 校验验证码
     */
    public static function check($mobile, $code, $event = 'default')
    {
        try {
            $time = time() - self::EXPIRE_TIME;
            $sms = self::where(['mobile' => $mobile, 'event' => $event])
                ->orderBy('id', 'DESC')
                ->first();

            if (!$sms) {
                $result = false;
            } elseif ($sms['createtime'] <= $time || $sms['times'] > self::MAX_CHECK_NUMS) {
                self::flush($mobile, $event);
                $result = false;
            } else {
                $is_correct = $code == $sms['code'];
                if (!$is_correct) {
                    Db::table('sms')->where(['id' => $sms['id'], 'event' => $event])
                        ->update(['times' => $sms->times + 1]);
                    $result = false;
                } else {
                    $result = true;
                }
            }
        } catch (\Exception $e) {
            $result = false;
        }

        return $result;
    }

    /**
     * 清空指定手机号验证码
     */
    public static function flush($mobile, $event = 'default')
    {
        try {
            self::where(['mobile' => $mobile, 'event' => $event])->delete();
            $result = true;
        } catch (\Exception $e) {
            $result = false;
        }

        return $result;
    }
}
