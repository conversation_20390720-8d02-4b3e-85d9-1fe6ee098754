<?php

declare(strict_types=1);

if (!function_exists('timeAgo')) {
    function timeAgo($timestamp)
    {
        $time_difference = time() - $timestamp;

        if ($time_difference < 60) {
            return $time_difference . '秒前';
        } elseif ($time_difference < 3600) {
            return floor($time_difference / 60) . '分钟前';
        } elseif ($time_difference < 86400) {
            return floor($time_difference / 3600) . '小时前';
        } else {
            $days = floor($time_difference / 86400);
            return min($days, 3) . '天前';
        }
    }
}

if (!function_exists('language')) {
    function language($value, $to = "zh", $from = "auto")
    {
        $appid = env('BAIDU_APP_KEY');
        $key = env('BAIDU_APP_SECRET');
        $salt = rand(1000000000, 9999999999);
        $sign = md5($appid . $value . $salt . $key);
        $value_encoded = urlencode($value);

        $url = "http://api.fanyi.baidu.com/api/trans/vip/translate?q=$value_encoded&appid=$appid&salt=$salt&from=$from&to=$to&sign=$sign";
        $response = json_decode(file_get_contents($url), true);

        if (isset($response['error_code']) && $response['error_code'] != 52000) {
            return '';
        }

        $result = '';
        foreach ($response['trans_result'] as $item) {
            $result .= ucwords($item['dst']);
        }

        return $result;
    }
}


if (!function_exists("getEncryptPassword")) {
    function getEncryptPassword($password, $salt = '')
    {
        return md5(md5($password) . $salt);
    }
}

if (!function_exists('alnum')) {
    function alnum($len = 6)
    {
        $pool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        return substr(str_shuffle(str_repeat($pool, intval(ceil($len / strlen($pool))))), 0, $len);
    }
}

if (!function_exists('rand_invite_code')) {
    function rand_invite_code($len = 4)
    {
        $chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $result = '';
        for ($i = 0; $i < $len; $i++) {
            $result .= $chars[mt_rand(0, 61)];
        }
        return $result;
    }
}

if (!function_exists('http_request')) {
    function http_request($url, $data = null, $header = null)
    {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
        ]);

        if (!empty($header)) {
            curl_setopt($curl, CURLOPT_HEADER, false);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        }

        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }

        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }
}

if (!function_exists('get_age')) {
    function get_age($birthday)
    {
        if (preg_match('/^\d{10,}$/', $birthday)) {
            $birthday = date('Y-m-d', $birthday);
        }

        $current = explode('-', date('Y-m-d'));
        $birth = explode('-', $birthday);

        $age = $current[0] - $birth[0];
        if ($current[1] < $birth[1] || ($current[1] == $birth[1] && $current[2] < $birth[2])) {
            $age--;
        }

        return $age;
    }
}

if (!function_exists('uuid')) {
    function uuid($user_id = 0)
    {
        return sprintf(
            '%04x%04x%04x%04x%04x%04x%04x%04x%s%s',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            $user_id,
            time()
        );
    }
}

if (!function_exists('success')) {
    function success($msg = '', $data = '', $code = '1')
    {
        return ['code' => $code, 'msg' => $msg, 'data' => $data];
    }
}

if (!function_exists('error')) {
    function error($msg = '', $data = '', $code = '0')
    {
        return ['code' => $code, 'msg' => $msg, 'data' => $data];
    }
}

if (!function_exists('is_num')) {
    function is_num($x = '')
    {
        return preg_match('/^[1-9]\d*$/', (string)$x);
    }
}


if (!function_exists('choose_lang')) {
    function choose_lang($lang)
    {
        $lang_map = [
            'zh-cn' => '',
            'zh-hk' => 'hk'
        ];

        return $lang_map[$lang] ?? $lang;
    }
}

if (!function_exists('get_avatar')) {
    function get_avatar($url)
    {
        if (!$url) {
            $url = '/assets/img/avatar.png';
        }

        if (!preg_match('/^https?:\/\//', $url)) {
            return 'https://img.tomeetr.com' . $url;
        }

        return $url;
    }
}

if (!function_exists('half_avatar')) {
    function half_avatar($url)
    {
        if (!$url) {
            $url = '/assets/img/avatar.png';
        }

        $cdnurl = 'https://tonycdn.angeldating.live';
        if (preg_match('/^https?:\/\//', $url)) {
            return substr($url, strlen($cdnurl));
        }

        return $url;
    }
}


if (!function_exists('get_xingzuo')) {
    function get_xingzuo($birthday)
    {
        if (preg_match('/^\d{10,}$/', $birthday)) {
            $birthday = date('Y-m-d', $birthday);
        }

        $date_parts = explode('-', $birthday);
        $month = (int)$date_parts[1];
        $day = (int)$date_parts[2];

        if ($month < 1 || $month > 12 || $day < 1 || $day > 31) {
            return '';
        }

        $constellations = [
            [1, 20, 2, 18, '水瓶'],
            [2, 19, 3, 20, '双鱼'],
            [3, 21, 4, 19, '白羊'],
            [4, 20, 5, 20, '金牛'],
            [5, 21, 6, 21, '双子'],
            [6, 22, 7, 22, '巨蟹'],
            [7, 23, 8, 22, '狮子'],
            [8, 23, 9, 22, '处女'],
            [9, 23, 10, 23, '天秤'],
            [10, 24, 11, 22, '天蝎'],
            [11, 23, 12, 21, '射手'],
            [12, 22, 1, 19, '摩羯']
        ];

        foreach ($constellations as $constellation) {
            if (($month == $constellation[0] && $day >= $constellation[1]) ||
                ($month == $constellation[2] && $day <= $constellation[3])) {
                return $constellation[4];
            }
        }

        return '';
    }
}

if (!function_exists('last_time')) {
    function last_time($last_time)
    {
        $diff = time() - $last_time;

        $time_units = [
            31536000 => '年前',
            2592000 => '个月前',
            86400 => '天前',
            3600 => '个小时前',
            60 => '分钟前'
        ];

        foreach ($time_units as $seconds => $unit) {
            if ($diff >= $seconds) {
                return floor($diff / $seconds) . $unit;
            }
        }

        return $diff . '秒前';
    }
}


if (!function_exists('distance')) {
    function distance($lng1, $lat1, $lng2, $lat2)
    {
        $radLat1 = deg2rad($lat1);
        $radLat2 = deg2rad($lat2);
        $radLng1 = deg2rad($lng1);
        $radLng2 = deg2rad($lng2);
        $a = $radLat1 - $radLat2;
        $b = $radLng1 - $radLng2;
        $s = 2 * asin(sqrt(pow(sin($a / 2), 2) + cos($radLat1) * cos($radLat2) * pow(sin($b / 2), 2))) * 6378.137;
        return sprintf("%.2f", $s);
    }
}

function startWith($str, $needle)
{
    return strpos($str, $needle) === 0;
}

use GuzzleHttp\Client;
use Hyperf\Guzzle\CoroutineHandler;
use GuzzleHttp\HandlerStack;

if (!function_exists('ip_lng_lat')) {
    function ip_lng_lat($ip)
    {
        $client = new Client([
            'handler' => HandlerStack::create(new CoroutineHandler()),
            'timeout' => 5,
            'swoole' => [
                'timeout' => 5,
                'socket_buffer_size' => 1024 * 1024 * 2,
            ],
        ]);

        $response = $client->get("https://restapi.amap.com/v3/ip?ip={$ip}&key=e16a5f8e63362910e0005c54a50eba12");

        if ($response->getStatusCode() !== 200) {
            return ['lng' => '', 'lat' => '', 'city' => ''];
        }

        $data = json_decode($response->getBody()->getContents(), true);

        if (empty($data['rectangle'])) {
            return ['lng' => '', 'lat' => '', 'city' => ''];
        }

        $rectangles = explode(';', $data['rectangle']);
        $point1 = explode(',', $rectangles[0]);
        $point2 = explode(',', $rectangles[1]);

        return [
            'lng' => bcdiv(bcadd($point1[0], $point2[0], 8), '2', 8),
            'lat' => bcdiv(bcadd($point1[1], $point2[1], 8), '2', 8),
            'city' => $data['city']
        ];
    }
}
function get_city_by_ip($ip)
{
    $client = new Client([
        'handler' => HandlerStack::create(new CoroutineHandler()),
        'timeout' => 5,
        'swoole' => [
            'timeout' => 5,
            'socket_buffer_size' => 1024 * 1024 * 2,
        ],
    ]);

    $response = $client->get("https://restapi.amap.com/v3/ip?ip={$ip}&key=e16a5f8e63362910e0005c54a50eba12");

    if ($response->getStatusCode() !== 200) {
        return '';
    }

    $data = json_decode($response->getBody()->getContents(), true);
    return empty($data['rectangle']) ? '' : $data['city'];
}

use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Context\ApplicationContext;

function getRealIp(): string
{
    $request = ApplicationContext::getContainer()->get(RequestInterface::class);
    $headers = $request->getHeaders();

    if (!empty($headers['x-forwarded-for'][0])) {
        return $headers['x-forwarded-for'][0];
    }

    if (!empty($headers['x-real-ip'][0])) {
        return $headers['x-real-ip'][0];
    }

    $serverParams = $request->getServerParams();
    return $serverParams['remote_addr'] ?? '';
}

function rand_lnglat($lng, $lat)
{
    $lng_diff = 0.087;
    $lat_diff = 0.068;
    $divisions = 50000;

    $lng_sign = mt_rand(0, 1) ? 1 : -1;
    $lat_sign = mt_rand(0, 1) ? 1 : -1;

    $lng = floatval($lng) + $lng_sign * $lng_diff * (mt_rand(0, $divisions) / $divisions);
    $lat = floatval($lat) + $lat_sign * $lat_diff * (mt_rand(0, $divisions) / $divisions);

    return ['lng' => $lng, 'lat' => $lat];
}

function rand_time_point($start, $end, $times)
{
    $times = min($times, $end - $start);
    $result = [];

    while (count($result) < $times) {
        $timestamp = mt_rand($start, $end);
        if (!in_array($timestamp, $result)) {
            $result[] = $timestamp;
        }
    }

    return $result;
}


function get_city_lnglat($city_name)
{
    $client = new Client([
        'handler' => HandlerStack::create(new CoroutineHandler()),
        'timeout' => 5,
        'swoole' => [
            'timeout' => 5,
            'socket_buffer_size' => 1024 * 1024 * 2,
        ],
    ]);

    $response = $client->get("https://restapi.amap.com/v3/geocode/geo?address={$city_name}&key=e16a5f8e63362910e0005c54a50eba12");

    if ($response->getStatusCode() !== 200) {
        return ['lng' => '', 'lat' => ''];
    }

    $data = json_decode($response->getBody()->getContents(), true);

    if (!isset($data['geocodes'][0]['location'])) {
        return ['lng' => '', 'lat' => ''];
    }

    $coordinates = explode(',', $data['geocodes'][0]['location']);
    return ['lng' => $coordinates[0], 'lat' => $coordinates[1]];
}

function get_lnglat_city($lng)
{
    $client = new Client([
        'handler' => HandlerStack::create(new CoroutineHandler()),
        'timeout' => 5,
        'swoole' => [
            'timeout' => 5,
            'socket_buffer_size' => 1024 * 1024 * 2,
        ],
    ]);

    $response = $client->get("https://restapi.amap.com/v3/geocode/regeo?location=$lng&key=e16a5f8e63362910e0005c54a50eba12");

    if ($response->getStatusCode() !== 200) {
        return '';
    }

    $data = json_decode($response->getBody()->getContents(), true);

    if (isset($data['regeocode']['addressComponent']['city']) && $data['status'] == 1) {
        return $data['regeocode']['addressComponent']['city'];
    }

    return '';
}