<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\DbConnection\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

/**
 * @property int $id
 * @property int $from_id
 * @property int $to_id
 * @property int $createtime
 */
class Follow extends Model
{
    public bool $timestamps = false;

    protected ?string $table = 'follow';
    protected array $fillable = [];
    protected array $casts = [
        'id' => 'integer',
        'from_id' => 'integer',
        'to_id' => 'integer',
        'createtime' => 'integer'
    ];

    #[Inject]
    private Redis $redis;

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix', '');
    }

    /**
     * 获取用户别名
     */
    private function getUserAlias(int $user_id, int $target_user_id): string
    {
        $alias_name = $this->redis->hget($this->getRedisPrefix() . "alias_user_{$user_id}", (string)$target_user_id);
        return $alias_name === false ? '' : $alias_name;
    }

    /**
     * 处理用户基础信息
     */
    private function processUserInfo(array &$user, array $occupation): void
    {
        $user['avatar'] = get_avatar($user['avatar']);
        $user['height'] = $user['height'] ?? '';
        $user['age'] = $user['birthday'] === null ? 0 : get_age($user['birthday']);
        $user['occupation_arr'] = $user['occupation'] ? strtr($user['occupation'], $occupation) : '';
    }
    /**
     * 关注/取消关注用户
     */
    public function follow_user($user_id, $friend_id)
    {
        try {
            $prefix = $this->getRedisPrefix();
            $is_following = $this->redis->sismember("{$prefix}follow_user_{$user_id}", (string)$friend_id);

            if ($is_following == 0) {
                Db::transaction(function () use ($user_id, $friend_id, $prefix) {
                    self::insert(['from_id' => $user_id, 'to_id' => $friend_id, 'createtime' => time()]);
                    Db::table('user_base')->where('user_id', $user_id)->increment('follow_num', 1);
                    Db::table('user_base')->where('user_id', $friend_id)->increment('fans_num', 1);
                    $this->redis->sadd("{$prefix}follow_user_{$user_id}", (string)$friend_id);
                });
            } else {
                Db::transaction(function () use ($user_id, $friend_id, $prefix) {
                    self::where('from_id', $user_id)->where('to_id', $friend_id)->delete();
                    Db::table('user_base')->where('user_id', $user_id)->decrement('follow_num', 1);
                    Db::table('user_base')->where('user_id', $friend_id)->decrement('fans_num', 1);
                    $this->redis->srem("{$prefix}follow_user_{$user_id}", (string)$friend_id);
                });
            }

            $result = \Hyperf\Translation\trans('params.success');
        } catch (\Exception $e) {
            $result = \Hyperf\Translation\trans('params.error');
        }

        return $result;
    }


    /**
     * 获取关注列表
     */
    public function focuson_list($where, $page, $limit, $field)
    {
        try {
            $occupation = $this->redis->hgetall($this->getRedisPrefix() . 'global_select_code');

            $list = self::where($where)
                ->leftJoin('user', 'user.id', '=', "follow.{$field}")
                ->orderBy('follow.createtime', 'desc')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->select($field . ' as user_id', 'nickname', 'avatar', 'birthday', 'height', 'weight', 'occupation', 'girl_switch', 'is_vip')
                ->get();

            if (!$list) {
                $result = [];
            } else {
                $list = $list->toArray();
                $user_ids = array_column($list, 'user_id');

                if ($field == 'from_id') {
                    $followed_users = self::query()
                        ->where('from_id', $where['to_id'])
                        ->whereIn('to_id', $user_ids)
                        ->pluck('to_id')
                        ->toArray();

                    $current_user_id = $where['to_id'];
                } else {
                    $followed_users = [];
                    $current_user_id = $where['from_id'];
                }

                foreach ($list as &$user) {
                    $this->processUserInfo($user, $occupation);
                    $user['alias_name'] = $this->getUserAlias($current_user_id, $user['user_id']);

                    if ($field == 'from_id') {
                        $user['have_follow'] = in_array($user['user_id'], $followed_users) ? '1' : '0';
                    }
                }

                $result = $list;
            }
        } catch (\Exception $e) {
            $result = [];
        }

        return $result;
    }
}
