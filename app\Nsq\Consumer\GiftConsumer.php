<?php

declare(strict_types=1);

namespace App\Nsq\Consumer;

use Hyperf\Nsq\AbstractConsumer;
use Hyperf\Nsq\Annotation\Consumer;
use Hyperf\Nsq\Message;
use Hyperf\Nsq\Result;
use Hyperf\Redis\RedisFactory;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;

#[Consumer(topic: 'gift', channel: 'tangx', name: 'GiftConsumer', nums: 1)]
class GiftConsumer extends AbstractConsumer
{
    private $redis;
    private $prefix;

    public function __construct()
    {
        $container = ApplicationContext::getContainer();
        $this->redis = $container->get(RedisFactory::class)->get('default');
        $this->prefix = env('redis_prefix');
    }

    public function isEnable(): bool
    {
        return true;
    }

    public function consume(Message $payload): ?string
    {
        $arr = json_decode($payload->getBody(), true);

        if ($arr['action'] === 'send_gift') {
            $this->send_gift($arr);
        }

        return Result::ACK;
    }
    public function send_gift($params)
    {
        $user_id = $params['user_id'];
        $gift_id = $params['gift_id'];
        $friend_id = $params['friend_id'];
        $num = $params['num'];
        $gift = $params['gift'];
        $total = $params['total'];

        $user_data = Db::table('user')->whereIn('id', [$user_id, $friend_id])
            ->select('id', 'money', 'score')->get()->keyBy('id');

        $before_money = $user_data[$friend_id]['money'];
        $score = $user_data[$user_id]['score'];

        $rate = $this->redis->hget($this->prefix . 'global_config', 'money_rate');
        $money = $total * $rate * 0.01;

        if (bccomp(strval($money), '0', 2) == 1) {
            Db::table('user')->where('id', $friend_id)->increment('money', $money);
        }

        $current_time = time();

        $logs = [
            [
                'table' => 'user_score_log',
                'data' => [
                    'user_id' => $user_id,
                    'score' => -$total,
                    'before' => $score,
                    'after' => $score - $total,
                    'memo' => '送礼物',
                    'createtime' => $current_time,
                ]
            ],
            [
                'table' => 'user_money_log',
                'data' => [
                    'user_id' => $friend_id,
                    'money' => $money,
                    'before' => $before_money,
                    'after' => $before_money + $money,
                    'memo' => '收到礼物提成',
                    'createtime' => $current_time,
                ]
            ],
            [
                'table' => 'gift_log',
                'data' => [
                    'from_id' => $user_id,
                    'to_id' => $friend_id,
                    'gift_id' => $gift_id,
                    'num' => $num,
                    'gift_price' => $gift['price'],
                    'total_price' => $total,
                    'kind' => $gift['kind'],
                    'createtime' => $current_time,
                    'actual_money' => $money
                ]
            ]
        ];

        foreach ($logs as $log) {
            Db::table($log['table'])->insert($log['data']);
        }

        $this->updateGiftCount($friend_id, $gift_id, $num, $total, '2');
        $this->updateGiftCount($user_id, $gift_id, $num, $total, '1');
    }

    private function updateGiftCount($user_id, $gift_id, $num, $total, $be_send)
    {
        $existing_id = Db::table('gift_count')
            ->where('user_id', $user_id)
            ->where('gift_id', $gift_id)
            ->where('be_send', $be_send)
            ->value('id');

        if ($existing_id) {
            Db::update("UPDATE `py_gift_count` SET `num`=`num`+?,`total_price`=`total_price`+? WHERE `id` = ?",
                [$num, $total, $existing_id]);
        } else {
            Db::table('gift_count')->insert([
                'user_id' => $user_id,
                'gift_id' => $gift_id,
                'num' => $num,
                'total_price' => $total,
                'be_send' => $be_send
            ]);
        }
    }
}
