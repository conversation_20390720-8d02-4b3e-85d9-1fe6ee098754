<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\DbConnection\Model\Model;
use Hyperf\Utils\ApplicationContext;

class Visit extends Model
{
    protected ?string $table = 'visit';
    protected array $fillable = [];
    protected array $casts = [];

    private const SELECT_FIELDS = [
        'visit.createtime', 'nickname', 'avatar', 'gender', 'is_auth',
        'girl_switch', 'is_vip', 'birthday', 'height', 'forever_on', 'occupation'
    ];

    public static function visit_list($user_id, $page, $limit)
    {
        $list = self::buildQuery('to_id', $user_id, $page, $limit)->get();
        return $list->isEmpty() ? [] : self::processVisitData($list->toArray(), $user_id);
    }

    public static function me_visit_list($user_id, $page, $limit)
    {
        $list = self::buildQuery('from_id', $user_id, $page, $limit, 'from_id')->get();
        return $list->isEmpty() ? [] : self::processVisitData($list->toArray(), $user_id);
    }

    private static function buildQuery($joinField, $user_id, $page, $limit, $whereField = 'from_id')
    {
        $selectFields = array_merge(["{$joinField} as user_id"], self::SELECT_FIELDS);

        return self::query()
            ->leftJoin('user', 'user.id', '=', "visit.{$joinField}")
            ->where($whereField, $user_id)
            ->orderBy('visit.createtime', 'desc')
            ->offset(($page - 1) * $limit)
            ->limit($limit)
            ->select($selectFields);
    }

    private static function processVisitData($list, $user_id)
    {
        $redis = ApplicationContext::getContainer()->get(\Hyperf\Redis\Redis::class);
        $prefix = env('redis_prefix');
        $occupation = $redis->hgetall($prefix . 'global_select_code');

        foreach ($list as $x => &$y) {
            if ($y['birthday'] === null) {
                $list[$x]['age'] = 0;
                $y['birthday'] = '';
            } else {
                $y['age'] = get_age($y['birthday']);
            }

            if ($y['occupation'] === null) {
                $y['occupation'] = $y['occupation_arr'] = '';
            } else {
                $y['occupation_arr'] = strtr($y['occupation'], $occupation);
            }

            $list[$x]['avatar'] = get_avatar($y['avatar']);
            $list[$x]['createtime_attr'] = last_time($y['createtime']);

            $alias_name = $redis->hget("{$prefix}alias_user_{$user_id}", "{$y['user_id']}");
            $list[$x]['alias_name'] = $alias_name === false ? '' : $alias_name;
        }

        return $list;
    }
}
