<?php

declare(strict_types=1);

namespace App\Controller;

use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\DbConnection\Db;
use Yansongda\HyperfPay\Pay;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use App\Model\User;

#[AutoController]
class OrderController extends AbstractController
{
    #[Inject]
    private Redis $redis;

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix', '');
    }

    /**
     * 验证订单信息
     */
    private function validateOrder(string $order_sn): array
    {
        $type = substr($order_sn, 0, 2);

        if ($type === 'VI') {
            $info = Db::table('vip_order')->where('order_sn', $order_sn)->first();
            $money_field = 'price';
            $text = \Hyperf\Translation\trans('params.buyVip');
        } elseif ($type === 'RE') {
            $info = Db::table('recharge_order')->where('order_sn', $order_sn)->first();
            $money_field = 'amount';
            $text = \Hyperf\Translation\trans('params.rechargeDiamond');
        } else {
            throw new \Exception(\Hyperf\Translation\trans('params.orderError'));
        }

        if (!$info) {
            throw new \Exception(\Hyperf\Translation\trans('params.orderError'));
        }

        if ($info->status == '2') {
            throw new \Exception(\Hyperf\Translation\trans('params.expired'));
        }

        if ($info->status == '1') {
            throw new \Exception(\Hyperf\Translation\trans('params.paid'));
        }

        return [
            'info' => $info,
            'money' => $info->$money_field,
            'text' => $text
        ];
    }

    /**
     * 处理支付回调数据
     */
    private function processPaymentCallback($result, string $pay_type): void
    {
        $data = [
            'out_trade_no' => $result->out_trade_no,
            'trade_no' => $result->notify_id,
            'total_amount' => $result->buyer_pay_amount,
        ];

        $prefix = $this->getRedisPrefix();
        $this->redis->hmset("{$prefix}order_sn_{$result->out_trade_no}", $data);

        http_request('http://ywtcweb.yewan8888.com/addons/epay/api/notifyx', [
            'order_sn' => $result->out_trade_no,
            'type' => $pay_type
        ]);
    }

    public function pay(RequestInterface $req, Pay $pay)
    {
        try {
            $order_sn = $req->input('order_sn', '');
            $pay_type = $req->input('pay_type', '');

            if (!in_array($pay_type, ['1', '2'])) {
                throw new \Exception(\Hyperf\Translation\trans('params.choosePayType'));
            }

            $order_data = $this->validateOrder($order_sn);

            if ($pay_type === '1') {
                $result = $pay->alipay()->app([
                    'out_trade_no' => $order_sn . time(),
                    'total_amount' => $order_data['money'],
                    'subject' => $order_data['text'],
                ]);
            } else {
                throw new \Exception(\Hyperf\Translation\trans('params.error'));
            }
        } catch (\Exception $e) {
            return error($e->getMessage());
        }

        return $result;
    }


    public function notify_pay(Pay $pay)
    {
        return $pay->alipay()->web([
            'out_trade_no' => 'RE' . time(),
            'total_amount' => '0.01',
            'subject' => 'yansongda 测试 - 1',
        ]);
    }

    /**
     * 支付宝支付成功回调
     */
    public function alipay(RequestInterface $req, Pay $pay)
    {
        try {
            $result = $pay->alipay()->callback($req->all());
            $this->processPaymentCallback($result, 'alipay');
        } catch (\Exception $e) {
            return error($e->getMessage());
        }

        return $pay->alipay()->success();
    }

    /**
     * 微信支付成功回调
     */
    public function wechat(RequestInterface $req, Pay $pay)
    {
        try {
            $result = $pay->alipay()->callback($req->all());
            $this->processPaymentCallback($result, 'wechat');
        } catch (\Exception $e) {
            return error($e->getMessage());
        }

        return $pay->alipay()->success();
    }

    /**
     * 处理VIP订单
     */
    protected function vip_buy_order(string $order_sn, string $type): void
    {
        $info = Db::table('vip_order')->where('order_sn', $order_sn)->first();

        if (empty($info) || $info['status'] != '0') {
            return;
        }

        $save = [
            'status' => '1',
            'pay_type' => $type,
            'pay_time' => time(),
        ];

        Db::table('vipbuylog')->where('id', $info['id'])->update($save);
        User::vip_update_user($info['user_id'], $info['month'], $info['unit'], '1', '111', $type);
    }
}
