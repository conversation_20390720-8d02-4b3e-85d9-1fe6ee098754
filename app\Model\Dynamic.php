<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\DbConnection\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

/**
 * @property int $id
 * @property int $user_id
 * @property string $content
 * @property int $createtime
 * @property string $is_hot
 * @property int $great_num
 * @property int $look_num
 * @property int $comments_num
 * @property string $have_source
 * @property string $location
 * @property int $status_switch
 */
class Dynamic extends Model
{
    public bool $timestamps = false;

    protected ?string $table = 'dynamic';
    protected array $fillable = [];
    protected array $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'createtime' => 'integer',
        'great_num' => 'integer',
        'look_num' => 'integer',
        'comments_num' => 'integer',
        'status_switch' => 'integer'
    ];

    #[Inject]
    private Redis $redis;

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix', '');
    }

    /**
     * 构建图片列表
     */
    private function buildImageList(array $images, int $dynamic_id): array
    {
        $img_list = [];
        foreach ($images as $image) {
            if ($dynamic_id == $image['dynamic_id']) {
                $img_list[] = [
                    'id' => $image['id'],
                    'image' => get_avatar($image['image']),
                    'is_qualified' => $image['is_qualified'],
                    'source_type' => $image['source_type'],
                ];
            }
        }
        return $img_list;
    }

    /**
     * 获取用户别名
     */
    private function getUserAlias(int $user_id, int $target_user_id): string
    {
        $alias_name = $this->redis->hget($this->getRedisPrefix() . "alias_user_{$user_id}", (string)$target_user_id);
        return $alias_name === false ? '' : $alias_name;
    }



    public function dynamic_list($user_id, $page, $limit, $to_id, $key_word, $gender, $hot_top, $my_city, $city_name, $my_follow, $follow_me, $lng, $lat)
    {
        try {
            $list = self::query()
                ->select(['dynamic.*','nickname','country','avatar','is_vip','user.birthday','user.is_auth','user.gender','user.girl_switch','user.bio','user.vip_type'])
                ->join('user','user.id','=','dynamic.user_id')
                ->where('dynamic.status','1')
                ->where('have_exit',0)
                ->where('user.status','normal')
                ->whereNotIn('user_id',function($sql)use($user_id){
                    $sql->from('forbid_user')->where('from_id',$user_id)->select('to_id');
                })
                ->when(is_num($to_id),function($sql)use($to_id){
                    $sql->where('user_id',$to_id);
                })
                ->when($key_word!='',function($sql)use($key_word){
                    $sql->where('content','like','%'.$key_word.'%')
                    ->orWhere('nickname','like','%'.$key_word.'%');
                })
                ->when($hot_top=='1',function($sql){
                    $sql->where('is_hot','1');
                })
                ->when($my_city!='',function ($sql)use($lng,$lat){
                    $sql->whereNotNull('dynamic.lng')
                    ->whereNotNull('dynamic.lat')
                    ->addSelect(Db::raw("ROUND(6378.138*2*ASIN(SQRT(POW(SIN(({$lat}*PI()/180-py_dynamic.lat*PI()/180)/2),2)+COS({$lat}*PI()/180)*COS(py_dynamic.lat*PI()/180)*POW(SIN(({$lng}*PI()/180-py_dynamic.lng*PI()/180)/2),2)))*1000) AS distance"))->orderBy('distance','asc');
                },function($sql){
                    $sql->orderBy('dynamic.createtime','desc');
                })
                ->when($city_name!='',function($sql)use($city_name){
                    $sql->where('location',$city_name);
                })
                ->when($my_follow=='1',function ($sql)use($user_id){
                    $sql->whereIn('user_id',function ($sql2)use($user_id){
                        $sql2->from('follow')->where('from_id',$user_id)->select('to_id');
                    });
                })
                ->when($follow_me=='1',function ($sql)use($user_id){
                    $sql->whereIn('user_id',function ($sql2)use($user_id){
                        $sql2->from('follow')->where('to_id',$user_id)->select('from_id');
                    });
                })
                ->when(($to_id=='' && $key_word=='' && $hot_top!='1' &&$city_name==''&&$my_follow!='1'&&$follow_me!='1'),function($sql)use($gender,$user_id){
                    $sql->where('gender',$gender)->orWhere('user_id',$user_id);
                })
                ->offset(($page-1)*10)
                ->limit($limit)
                ->get();

            if (!$list) {
                $result = ['list' => []];
            } else {
                $list = $list->toArray();
                $ids = $user_ids = [];
                foreach ($list as $item) {
                    $ids[] = $item['id'];
                    $user_ids[] = $item['user_id'];
                }

                $result = $this->processDynamicList($list, $ids, $user_ids, $user_id);
            }
        } catch (\Exception $e) {
            $result = ['list' => []];
        }

        return $result;
    }

    /**
     * 处理动态列表数据
     */
    private function processDynamicList(array $list, array $ids, array $user_ids, int $user_id): array
    {
        $images = DynamicImg::query()->whereIn('dynamic_id', $ids)->select('id','image','is_qualified','dynamic_id','source_type')->get()->toArray();
        $liked_dynamics = DynamicGreat::query()->where('user_id', $user_id)->whereIn('dynamic_id', $ids)->pluck('dynamic_id')->toArray();
        $followed_users = Follow::query()->where('from_id', $user_id)->whereIn('to_id', $user_ids)->pluck('to_id')->toArray();

        foreach ($list as &$item) {
            $item['avatar'] = get_avatar($item['avatar']);
            $item['alias_name'] = $this->getUserAlias($user_id, $item['user_id']);
            $item['is_zan'] = in_array($item['id'], $liked_dynamics) ? '1' : '0';
            $item['is_follow'] = in_array($item['user_id'], $followed_users) ? '1' : '0';
            $item['age'] = $item['birthday'] === null ? '0' : get_age($item['birthday']);
            $item['createtime_txt'] = last_time($item['createtime']);
            $item['img_list'] = $this->buildImageList($images, $item['id']);
            $item["comment_list_preview"] = $this->getCommentPreview($item["id"], $followed_users);
        }

        return ['list' => $list];
    }

    /**
     * 获取评论预览
     */
    private function getCommentPreview(int $dynamic_id, array $followed_users): array
    {
        return DynamicComments::query()
            ->with("user:id,nickname,avatar,gender")
            ->where("dynamic_id", $dynamic_id)
            ->selectRaw("id,user_id,comments,createtime,cmt_num")
            ->orderBy("id", "desc")
            ->limit(2)
            ->get()
            ->map(function ($item) use ($followed_users) {
                return [
                    "id" => $item->id,
                    "user_id" => $item->user,
                    "comments" => $item->comments,
                    "createtime" => $item->createtime,
                    "cmt_num" => $item->cmt_num,
                    "is_follow" => in_array($item->user_id, $followed_users) ? 1 : 0,
                ];
            })->toArray();
    }
    //,'sql1'=>$sql1,'sql2'=>$sql2,



    public function dynamic_info($user_id, $dynamic_id)
    {
        try {
            $list = self::query()
                ->join('user','user.id','=','dynamic.user_id')
                ->where('dynamic.id', $dynamic_id)
                ->select('dynamic.*','nickname','avatar','user.gender','is_vip','user.birthday','user.is_auth','user.girl_switch')
                ->first();

            if (!$list) {
                $result = [];
            } else {
                $list = $list->toArray();
                $images = DynamicImg::query()->where('dynamic_id', $dynamic_id)->select('id','image','is_qualified','dynamic_id','source_type')->get()->toArray();
                $is_liked = DynamicGreat::query()->where('user_id', $user_id)->where('dynamic_id', $dynamic_id)->exists();
                $is_followed = Follow::query()->where('from_id', $user_id)->where('to_id', $list['user_id'])->exists();

                $list['avatar'] = get_avatar($list['avatar']);
                $list['is_zan'] = $is_liked ? '1' : '0';
                $list['is_follow'] = $is_followed ? '1' : '0';
                $list['age'] = $list['birthday'] === null ? '0' : get_age($list['birthday']);
                $list['createtime_txt'] = last_time($list['createtime']);
                $list['alias_name'] = $this->getUserAlias($user_id, $list['user_id']);
                $list['img_list'] = $this->buildImageList($images, $list['id']);

                $result = $list;
            }
        } catch (\Exception $e) {
            $result = [];
        }

        return $result;
    }





    /**
     * 关注/取消关注用户
     */
    public static function follow_user($user_id, $friend_id)
    {
        try {
            $user_exists = Db::table('user_base')->where('user_id', $friend_id)->exists();
            if (!$user_exists) {
                return null;
            }

            $follow_exists = Db::table('follow')->where('from_id', $user_id)->where('to_id', $friend_id)->exists();

            if (!$follow_exists) {
                Db::transaction(function () use ($user_id, $friend_id) {
                    Db::table('follow')->insert(['from_id' => $user_id, 'to_id' => $friend_id, 'createtime' => time()]);
                    Db::table('user_base')->where('user_id', $user_id)->increment('follow_num');
                    Db::table('user_base')->where('user_id', $friend_id)->increment('fans_num');
                });
                $result = '关注成功';
            } else {
                $follow_info = Db::table('follow')->where('from_id', $user_id)->where('to_id', $friend_id)->first();
                Db::transaction(function () use ($user_id, $friend_id, $follow_info) {
                    Db::table('follow')->where('id', $follow_info['id'])->delete();
                    Db::table('user_base')->where('user_id', $user_id)->decrement('follow_num');
                    Db::table('user_base')->where('user_id', $friend_id)->decrement('fans_num');
                });
                $result = '已取消关注';
            }
        } catch (\Exception $e) {
            $result = null;
        }

        return $result;
    }
}
