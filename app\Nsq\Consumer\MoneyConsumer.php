<?php

declare(strict_types=1);

namespace App\Nsq\Consumer;

use Hyperf\Nsq\AbstractConsumer;
use Hyperf\Nsq\Annotation\Consumer;
use Hyperf\Nsq\Message;
use Hyperf\Nsq\Result;
use Hyperf\Redis\RedisFactory;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;
use App\Model\User as UserModel;

#[Consumer(topic: 'money', channel: 'tangx', name: 'MoneyConsumer', nums: 1)]
class MoneyConsumer extends AbstractConsumer
{
    private $redis;
    private $prefix;
    private $partner_rates;

    public function __construct()
    {
        $container = ApplicationContext::getContainer();
        $this->redis = $container->get(RedisFactory::class)->get('default');
        $this->prefix = env('redis_prefix');
        $this->partner_rates = Db::table('partner_level')->pluck('rate', 'id');
    }

    public function isEnable(): bool
    {
        return true;
    }

    public function consume(Message $payload): ?string
    {
        $arr = json_decode($payload->getBody(), true);

        if ($arr['action'] === 'parent_get_money') {
            $this->parent_get_money($arr);
        }

        return Result::ACK;
    }


    public function parent_get_money($arr)
    {
        $config = $this->redis->hmget($this->prefix . 'global_config', ['money_rate', 'pu_parent_rate']);
        $money_rate = $config['money_rate'];
        $num = $arr['num'];

        $user = Db::table('user')->where('user.id', $arr['user_id'])
            ->leftJoin('user as b', 'b.id', '=', 'user.p_user')
            ->select('user.id', 'user.p_user', 'b.is_vip', 'b.partner_id', 'b.is_partner', 'b.partner_status', 'b.p_user as pp_user')
            ->first();

        if (!$user['p_user']) {
            return;
        }

        $msg = $this->getTypeMessage($arr['type'], $arr['user_id']);

        if ($user['partner_id'] && $user['is_partner'] && $user['partner_status'] == '2') {
            $this->processPartnerReward($user, $num, $money_rate, $msg);
        } else {
            $this->processNormalReward($user, $num, $money_rate, $config['pu_parent_rate'], $msg);
        }
    }

    private function processPartnerReward($user, $num, $money_rate, $msg)
    {
        $rate = $this->partner_rates[$user['partner_id']];
        $p_money = sprintf("%.2f", $num * $rate * $money_rate * 0.0001);

        if ($p_money <= 0) {
            return;
        }

        UserModel::money($p_money, $user['p_user'], $msg, '3');
        $this->updateSonMoney($user['p_user'], $p_money);

        $this->processGrandparentReward($user['p_user'], $num, $money_rate, $p_money, $msg, '3');
    }

    private function processNormalReward($user, $num, $money_rate, $pu_parent_rate, $msg)
    {
        $rate = $pu_parent_rate;
        $p_money = sprintf("%.2f", $num * $rate * $money_rate * 0.0001);

        if ($p_money <= 0) {
            return;
        }

        if ($user['is_vip'] != 'one') {
            $p_money = 0;
            return;
        }

        UserModel::money($p_money, $user['p_user'], $msg, '2');
        $this->updateSonMoney($user['p_user'], $p_money);

        $this->processGrandparentReward($user['p_user'], $num, $money_rate, $p_money, $msg, '2');
    }

    private function processGrandparentReward($parent_id, $num, $money_rate, $parent_money, $msg, $type)
    {
        $p_user = Db::table('user')->where('id', $parent_id)->value('p_user');

        if (!$p_user) {
            return;
        }

        $pp_user = Db::table('user')
            ->where('id', $p_user)
            ->select('id', 'partner_id', 'is_partner', 'partner_status', 'son_money')
            ->first();

        if (!($pp_user['partner_id'] && $pp_user['is_partner'] && $pp_user['partner_status'] == '2')) {
            return;
        }

        $rate = $this->partner_rates[$pp_user['partner_id']];
        $pp_money = sprintf("%.2f", $num * $rate * $money_rate * 0.0001);
        $pp_money = bcsub($pp_money, strval($parent_money), 2);

        if ($pp_money > 0) {
            $this->updateSonMoney($pp_user['id'], $pp_money);
            UserModel::money($pp_money, $pp_user['id'], $msg, $type);
        }
    }

    private function updateSonMoney($user_id, $money)
    {
        $current_money = Db::table('user')->where('id', $user_id)->value('son_money');
        Db::table('user')->where('id', $user_id)->update(['son_money' => bcadd($current_money, strval($money), 2)]);
    }

    private function getTypeMessage($type, $user_id)
    {
        $messages = [
            'send_gift' => '团队送礼物奖励-',
            'unlock_chatup' => '团队解锁私聊奖励-',
            'unlock_wx' => '团队解锁微信奖励-',
            'look_photo' => '团队查看相册奖励-',
            'video_online' => '团队视频通话奖励-',
            'voice_online' => '团队语音通话奖励-',
            'buy_vip' => '团队会员购买奖励-',
            'recharge' => '团队钻石购买奖励-'
        ];

        return ($messages[$type] ?? '团队奖励-') . $user_id;
    }

}