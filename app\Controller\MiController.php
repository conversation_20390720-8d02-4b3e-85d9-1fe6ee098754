<?php

declare(strict_types=1);

namespace App\Controller;

use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\DbConnection\Db;
use Hyperf\HttpServer\Annotation\Middlewares;
use App\Middleware\Auth\TokenMiddleware;
use Hyperf\Context\Context;
use RongCloud\RongCloud;
use App\Model\User;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

#[AutoController]
#[Middlewares([TokenMiddleware::class])]
class MiController extends AbstractController
{
    const APPKEY = 'c9kqb3rdcj85j';
    const APPSECRET = '0qtEgZXScD3A';

    #[Inject]
    private Redis $redis;

    private ?RongCloud $rongSDK = null;

    /**
     * 获取当前用户ID
     */
    private function getCurrentUserId(): int
    {
        $user_id = Context::get('user_id', 0);
        if (!$user_id) {
            throw new \Exception(\Hyperf\Translation\trans('params.loginError'), 10001);
        }
        return (int) $user_id;
    }

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix');
    }

    /**
     * 获取RongCloud SDK实例（单例模式）
     */
    private function getRongSDK(): RongCloud
    {
        if ($this->rongSDK === null) {
            $this->rongSDK = new RongCloud(self::APPKEY, self::APPSECRET);
        }
        return $this->rongSDK;
    }

    /**
     * 统一异常处理
     */
    private function handleException(\Exception $e): array
    {
        return error($e->getMessage(), null, $e->getCode() ?: '10001');
    }

    /**
     * 拉黑/取消拉黑用户
     */
    public function forbid_user(RequestInterface $req)
    {
        try {
            $user_id = $this->getCurrentUserId();
            $uid = $req->input('uid', '');

            if (!is_num($uid)) {
                return error(\Hyperf\Translation\trans('params.codeError'), (object)[]);
            }

            $prefix = $this->getRedisPrefix();
            $redisKey = "{$prefix}forbid_user_{$user_id}";
            $isBlocked = $this->redis->sismember($redisKey, (string)$uid);

            $rongSDK = $this->getRongSDK();
            $blacklistData = [
                'id' => (string)$user_id,
                'blacklist' => [(string)$uid]
            ];

            if ($isBlocked) {
                // 取消拉黑
                Db::table('forbid_user')->where(['from_id' => $user_id, 'to_id' => $uid])->delete();
                $this->redis->srem($redisKey, (string)$uid);
                $rongSDK->getUser()->Blacklist()->remove($blacklistData);
            } else {
                // 添加拉黑
                Db::table('forbid_user')->insert([
                    'from_id' => $user_id,
                    'to_id' => $uid,
                    'createtime' => time()
                ]);
                $this->redis->sadd($redisKey, (string)$uid);
                $rongSDK->getUser()->Blacklist()->add($blacklistData);
            }

        } catch (\Exception $e) {
            return $this->handleException($e);
        }

        return success(\Hyperf\Translation\trans('params.success'), (object)[]);
    }


    /**
     * 依次打招呼
     */
    public function chat_up_user(int $user_id, int $gender): void
    {
        $need_gender = $gender !== 2 ? 2 : 1;
        $userIds = User::query()
            ->where('forever_on', '1')
            ->where('gender', $need_gender)
            ->inRandomOrder()
            ->limit(3)
            ->pluck('id')
            ->toArray();

        if (empty($userIds)) {
            return;
        }

        $prefix = $this->getRedisPrefix();
        $greetingKey = $gender === 1 ? 'greeting_y' : 'greeting_x';

        foreach ($userIds as $targetUserId) {
            $word = $this->redis->srandmember($prefix . $greetingKey);
            if ($word) {
                $this->send_msg($targetUserId, $user_id, $word);
            }
        }
    }

    /**
     * 发送消息
     */
    private function send_msg(int $from_id, int $to_id, string $data): void
    {
        $rongSDK = $this->getRongSDK();
        $message = [
            'senderId' => (string)$from_id,
            'targetId' => (string)$to_id,
            'objectName' => 'RC:TxtMsg',
            'content' => json_encode(['content' => $data], JSON_UNESCAPED_UNICODE)
        ];

        $rongSDK->getMessage()->Person()->send($message);
    }
}   