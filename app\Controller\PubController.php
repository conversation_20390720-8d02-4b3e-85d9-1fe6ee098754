<?php

declare(strict_types=1);

namespace App\Controller;

use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

#[AutoController]
class PubController extends AbstractController
{
    #[Inject]
    private Redis $redis;

    private const VALID_TYPES = ['android', 'ios'];

    /**
     * 获取Redis前缀
     */
    private function getRedisPrefix(): string
    {
        return env('redis_prefix', '');
    }

    /**
     * 生成匿名用户名
     */
    private function generateAnonymousName(string $name): string
    {
        $strlen = mb_strlen($name, 'UTF-8');
        $firstStr = mb_substr($name, 0, 1, 'UTF-8');
        $lastStr = mb_substr($name, -1, 1, 'UTF-8');

        return $strlen == 2
            ? $firstStr . str_repeat('*', $strlen - 1)
            : $firstStr . str_repeat('*', $strlen - 2) . $lastStr;
    }

    /**
     * 简单查询个人信息
     */
    public function simple_user(RequestInterface $req)
    {
        try {
            $user_id = $req->input('user_id', '');
            if (!is_numeric($user_id)) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            $info = Db::table('user')
                ->where('id', $user_id)
                ->select('id as user_id', 'nickname', 'avatar', 'is_vip', 'girl_switch', 'forever_on')
                ->first();

            if (!$info) {
                $result = (object)[];
            } else {
                $info['avatar'] = get_avatar($info['avatar']);
                $prefix = $this->getRedisPrefix();
                $image = $this->redis->hget($prefix . 'global_config', 'bg_home_image');
                $info['bg_home'] = get_avatar($image);
                $result = $info;
            }
        } catch (\Exception $e) {
            return error($e->getMessage());
        }

        return success('', $result);
    }

    /**
     * 滚动随机用户消息
     */
    public function rand_user()
    {
        $prefix = $this->getRedisPrefix();
        $list = [];
        $danwei = [
            \Hyperf\Translation\trans('params.second'),
            \Hyperf\Translation\trans('params.minute'),
            \Hyperf\Translation\trans('params.hour')
        ];

        for ($i = 1; $i <= 10; $i++) {
            $x = mt_rand(0, 2);
            $y = ($x == 2) ? mt_rand(1, 12) : mt_rand(1, 59);

            $name = $this->redis->srandmember($prefix . 'hot_nickname');
            $anonymousName = $this->generateAnonymousName($name);

            $list[] = $anonymousName . \Hyperf\Translation\trans('params.msgBefore') . $y . $danwei[$x] . \Hyperf\Translation\trans('params.msgAfter');
        }

        return success('', $list);
    }

    /**
     * 滚动随机VIP用户消息
     */
    public function rand_vip_user()
    {
        $prefix = $this->getRedisPrefix();
        $list = [];

        for ($i = 1; $i <= 10; $i++) {
            $amount = mt_rand(10, 99);
            $name = $this->redis->srandmember($prefix . 'hot_nickname');
            $anonymousName = $this->generateAnonymousName($name);

            $list[] = $anonymousName . \Hyperf\Translation\trans('params.msgBeforeTwo') . $amount . '.00' . \Hyperf\Translation\trans('params.money');
        }

        return success('', $list);
    }


    /**
     * 版本更新
     */
    public function version(RequestInterface $req)
    {
        $type = $req->input('type', 'android');
        if (!in_array($type, self::VALID_TYPES)) {
            $type = 'android';
        }

        $version = Db::table('version')
            ->where('type_status', $type)
            ->select('type_status', 'newversion', 'packagesize', 'content', 'downloadurl', 'on_switch')
            ->orderBy('id', 'desc')
            ->first();

        return success('', $version);
    }

    /**
     * 版本上传检查
     */
    public function version_upload(RequestInterface $req)
    {
        try {
            $type = $req->input('type', 'android');
            $platform = $req->input('platform', '1');
            $newversion = $req->input('newversion', '');

            if (!in_array($type, self::VALID_TYPES)) {
                $type = 'android';
            }

            if ($type == 'ios') {
                $platform = '6';
            }

            if (empty($newversion)) {
                throw new \Exception(\Hyperf\Translation\trans('params.versionNot'));
            }

            $version = Db::table('version')
                ->where('type_status', $type)
                ->where('platform', $platform)
                ->where('newversion', $newversion)
                ->select('type_status', 'newversion', 'content', 'downloadurl', 'status')
                ->orderBy('id', 'desc')
                ->first();

            $result = $version ?? (object)[];
        } catch (\Exception $e) {
            return error($e->getMessage());
        }

        return success('', $result);
    }

    /**
     * 查看邀请码状态
     */
    public function invite_code_if_effective(RequestInterface $req)
    {
        try {
            $invite_code = $req->input('invite_code', '');
            if (empty($invite_code)) {
                throw new \Exception(\Hyperf\Translation\trans('params.codeError'));
            }

            $prefix = $this->getRedisPrefix();
            $have = $this->redis->sismember($prefix . 'all_invite_code', $invite_code);
            if (!$have) {
                throw new \Exception(\Hyperf\Translation\trans('params.inviteCodeError'));
            }

            $info = Db::table('user')
                ->where('invite_code', $invite_code)
                ->select('id', 'is_vip', 'invite_code')
                ->first();

            if (!$info || $info['is_vip'] != 'one') {
                throw new \Exception(\Hyperf\Translation\trans('params.inviteCodeError'));
            }
        } catch (\Exception $e) {
            return error($e->getMessage());
        }

        return success('', '');
    }
}